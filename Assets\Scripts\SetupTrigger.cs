using UnityEngine;

/// <summary>
/// 设置触发器 - 提供多种方式触发游戏设置
/// </summary>
public class SetupTrigger : MonoBehaviour
{
    [Header("触发方式")]
    public bool setupOnStart = true;
    public KeyCode setupKey = KeyCode.Space;
    public KeyCode cleanupKey = KeyCode.C;
    
    private SimpleGameSetup gameSetup;
    
    void Start()
    {
        // 获取或创建GameSetup组件
        gameSetup = GetComponent<SimpleGameSetup>();
        if (gameSetup == null)
        {
            gameSetup = gameObject.AddComponent<SimpleGameSetup>();
        }
        
        if (setupOnStart)
        {
            gameSetup.SetupGame();
        }
    }
    
    void Update()
    {
        // 按键触发
        if (Input.GetKeyDown(setupKey))
        {
            Debug.Log("按下空格键 - 设置游戏环境");
            gameSetup.SetupGame();
        }
        
        if (Input.GetKeyDown(cleanupKey))
        {
            Debug.Log("按下C键 - 清理环境");
            CleanupEnvironment();
        }
    }
    
    /// <summary>
    /// 清理环境
    /// </summary>
    public void CleanupEnvironment()
    {
        GameObject ground = GameObject.Find("Ground");
        GameObject player = GameObject.Find("Player");
        
        if (ground != null) DestroyImmediate(ground);
        if (player != null) DestroyImmediate(player);
        
        Debug.Log("环境已清理");
    }
    
    /// <summary>
    /// UI按钮调用的设置方法
    /// </summary>
    public void OnSetupButtonClick()
    {
        Debug.Log("UI按钮触发 - 设置游戏环境");
        gameSetup.SetupGame();
    }
    
    /// <summary>
    /// UI按钮调用的清理方法
    /// </summary>
    public void OnCleanupButtonClick()
    {
        Debug.Log("UI按钮触发 - 清理环境");
        CleanupEnvironment();
    }
}
