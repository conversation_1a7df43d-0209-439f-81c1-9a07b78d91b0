Base path: 'D:/UNRTIY/<PERSON>an <PERSON>b/2022.3.61t2/Editor/Data', plugins path 'D:/UNRTIY/<PERSON>an <PERSON>b/2022.3.61t2/Editor/Data/PlaybackEngines'
Cmd: initializeCompiler

Cmd: compileSnippet
  insize=1993 file=Packages/com.unity.render-pipelines.universal/Shaders/Utils/ScreenSpaceAmbientOcclusion.shader name=Hidden/Universal Render Pipeline/ScreenSpaceAmbientOcclusion pass=SSAO_Occlusion ppOnly=0 stripLineD=0 buildPlatform=19 rsLen=0 pKW=UNITY_ENABLE_REFLECTION_BUFFERS UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP SHADER_API_DESKTOP UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_FULL_HDR uKW=_INTERLEAVED_GRADIENT _SOURCE_DEPTH_NORMALS _ORTHOGRAPHIC _SAMPLE_COUNT_HIGH dKW=_GBUFFER_NORMALS_OCT _BLUE_NOISE _SOURCE_DEPTH_LOW _SOURCE_DEPTH_MEDIUM _SOURCE_DEPTH_HIGH _SAMPLE_COUNT_LOW _SAMPLE_COUNT_MEDIUM UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 UNITY_PBS_USE_BRDF3 UNITY_NO_FULL_STANDARD_SHADER UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_HALF_PRECISION_FRAGMENT_SHADER_REGISTERS UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_RGBM_ENCODING UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 SHADER_API_MAX_VISIBLE_LIGHTS_16 SHADER_API_DECL_TEMPS_IN_MAIN SHADER_API_DECL_IMMCB_AS_CONST UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=0 lang=3 type=Fragment platform=d3d11 reqs=33 mask=6 start=27 ok=1 outsize=19950

