using UnityEngine;

/// <summary>
/// 超平坦角色移动控制器
/// 使用WASD键控制角色在XZ平面上移动
/// </summary>
public class PlayerController : MonoBehaviour
{
    [Header("移动设置")]
    public float moveSpeed = 5f;
    public float rotationSpeed = 720f;

    [Header("组件引用")]
    private CharacterController characterController;
    private Transform playerTransform;

    // 输入变量
    private Vector3 moveDirection;
    private float horizontalInput;
    private float verticalInput;
    
    void Start()
    {
        // 获取组件引用
        characterController = GetComponent<CharacterController>();
        playerTransform = transform;
        
        // 如果没有CharacterController组件，添加一个
        if (characterController == null)
        {
            characterController = gameObject.AddComponent<CharacterController>();
            characterController.radius = 0.5f;
            characterController.height = 2f;
            characterController.center = new Vector3(0, 1f, 0);
        }
    }
    
    void Update()
    {
        HandleInput();
        HandleMovement();
        HandleRotation();
    }
    
    /// <summary>
    /// 处理输入
    /// </summary>
    private void HandleInput()
    {
        horizontalInput = Input.GetAxis("Horizontal"); // A/D 或 左/右箭头
        verticalInput = Input.GetAxis("Vertical");     // W/S 或 上/下箭头
    }
    
    /// <summary>
    /// 处理移动
    /// </summary>
    private void HandleMovement()
    {
        // 计算移动方向（仅在XZ平面）
        moveDirection = new Vector3(horizontalInput, 0, verticalInput).normalized;
        
        // 应用移动速度
        Vector3 velocity = moveDirection * moveSpeed;
        
        // 添加重力（保持角色贴地）
        velocity.y = -9.81f;
        
        // 移动角色
        characterController.Move(velocity * Time.deltaTime);
    }
    
    /// <summary>
    /// 处理旋转（角色面向移动方向）
    /// </summary>
    private void HandleRotation()
    {
        if (moveDirection != Vector3.zero)
        {
            // 计算目标旋转
            Quaternion targetRotation = Quaternion.LookRotation(moveDirection);
            
            // 平滑旋转到目标方向
            playerTransform.rotation = Quaternion.RotateTowards(
                playerTransform.rotation, 
                targetRotation, 
                rotationSpeed * Time.deltaTime
            );
        }
    }
    
    /// <summary>
    /// 在Scene视图中绘制调试信息
    /// </summary>
    void OnDrawGizmosSelected()
    {
        // 绘制移动方向
        if (Application.isPlaying && moveDirection != Vector3.zero)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawRay(transform.position, moveDirection * 2f);
        }

        // 绘制角色控制器边界
        if (characterController != null)
        {
            Gizmos.color = Color.yellow;
            Vector3 center = transform.position + characterController.center;
            float radius = characterController.radius;
            float height = characterController.height;

            // 绘制胶囊体的简化版本（上下两个球体和中间的圆柱体）
            Gizmos.DrawWireSphere(center + Vector3.up * (height/2 - radius), radius);
            Gizmos.DrawWireSphere(center + Vector3.down * (height/2 - radius), radius);
        }
    }
}
