using UnityEngine;

/// <summary>
/// 基础玩家移动脚本 - 最简单可靠的版本
/// </summary>
public class BasicPlayerMovement : MonoBehaviour
{
    public float moveSpeed = 5f;
    public float rotationSpeed = 10f;
    
    private CharacterController controller;
    
    void Start()
    {
        controller = GetComponent<CharacterController>();
        Debug.Log("BasicPlayerMovement 初始化完成");
    }
    
    void Update()
    {
        if (controller == null) return;
        
        // 获取输入
        float horizontal = Input.GetAxis("Horizontal"); // A/D
        float vertical = Input.GetAxis("Vertical");     // W/S
        
        // 计算移动方向
        Vector3 direction = new Vector3(horizontal, 0, vertical);
        
        if (direction.magnitude > 0.1f)
        {
            // 移动
            Vector3 move = direction * moveSpeed * Time.deltaTime;
            move.y = -9.81f * Time.deltaTime; // 重力
            controller.Move(move);
            
            // 旋转面向移动方向
            Quaternion targetRotation = Quaternion.LookRotation(direction);
            transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotationSpeed * Time.deltaTime);
            
            Debug.Log($"移动中: 方向({direction.x:F1}, {direction.z:F1}), 位置({transform.position.x:F1}, {transform.position.z:F1})");
        }
        else
        {
            // 只应用重力
            controller.Move(new Vector3(0, -9.81f * Time.deltaTime, 0));
        }
        
        // 显示输入状态
        if (Input.anyKeyDown)
        {
            string keys = "";
            if (Input.GetKey(KeyCode.W)) keys += "W ";
            if (Input.GetKey(KeyCode.A)) keys += "A ";
            if (Input.GetKey(KeyCode.S)) keys += "S ";
            if (Input.GetKey(KeyCode.D)) keys += "D ";
            if (keys != "") Debug.Log($"按键: {keys}");
        }
    }
}
