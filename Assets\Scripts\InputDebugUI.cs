using UnityEngine;

/// <summary>
/// 输入调试UI
/// 在屏幕上显示当前输入状态，用于调试
/// </summary>
public class InputDebugUI : MonoBehaviour
{
    [Header("UI设置")]
    public bool showDebugInfo = true;
    public Vector2 uiPosition = new Vector2(10, 10);
    public int fontSize = 16;
    
    private GUIStyle textStyle;
    private PlayerController playerController;
    
    void Start()
    {
        // 查找玩家控制器
        playerController = FindObjectOfType<PlayerController>();
        
        // 设置文本样式
        textStyle = new GUIStyle();
        textStyle.fontSize = fontSize;
        textStyle.normal.textColor = Color.white;
        textStyle.fontStyle = FontStyle.Bold;
    }
    
    void OnGUI()
    {
        if (!showDebugInfo) return;
        
        // 设置GUI区域
        GUILayout.BeginArea(new Rect(uiPosition.x, uiPosition.y, 300, 200));
        
        // 显示标题
        GUILayout.Label("=== 输入调试信息 ===", textStyle);
        GUILayout.Space(10);
        
        // 显示输入状态
        DisplayInputInfo();
        
        // 显示玩家信息
        DisplayPlayerInfo();
        
        // 显示控制说明
        DisplayControlInfo();
        
        GUILayout.EndArea();
    }
    
    /// <summary>
    /// 显示输入信息
    /// </summary>
    private void DisplayInputInfo()
    {
        GUILayout.Label("输入状态:", textStyle);
        
        float horizontal = Input.GetAxis("Horizontal");
        float vertical = Input.GetAxis("Vertical");
        
        GUILayout.Label($"水平输入 (A/D): {horizontal:F2}", textStyle);
        GUILayout.Label($"垂直输入 (W/S): {vertical:F2}", textStyle);
        
        // 显示按键状态
        string pressedKeys = "";
        if (Input.GetKey(KeyCode.W)) pressedKeys += "W ";
        if (Input.GetKey(KeyCode.A)) pressedKeys += "A ";
        if (Input.GetKey(KeyCode.S)) pressedKeys += "S ";
        if (Input.GetKey(KeyCode.D)) pressedKeys += "D ";
        
        GUILayout.Label($"按下的键: {(string.IsNullOrEmpty(pressedKeys) ? "无" : pressedKeys)}", textStyle);
        GUILayout.Space(5);
    }
    
    /// <summary>
    /// 显示玩家信息
    /// </summary>
    private void DisplayPlayerInfo()
    {
        if (playerController != null)
        {
            GUILayout.Label("玩家状态:", textStyle);
            
            Transform playerTransform = playerController.transform;
            Vector3 position = playerTransform.position;
            Vector3 rotation = playerTransform.eulerAngles;
            
            GUILayout.Label($"位置: ({position.x:F1}, {position.y:F1}, {position.z:F1})", textStyle);
            GUILayout.Label($"旋转: Y={rotation.y:F1}°", textStyle);
            GUILayout.Space(5);
        }
    }
    
    /// <summary>
    /// 显示控制说明
    /// </summary>
    private void DisplayControlInfo()
    {
        GUILayout.Label("控制说明:", textStyle);
        GUILayout.Label("W - 向前移动", textStyle);
        GUILayout.Label("S - 向后移动", textStyle);
        GUILayout.Label("A - 向左移动", textStyle);
        GUILayout.Label("D - 向右移动", textStyle);
    }
    
    /// <summary>
    /// 切换调试信息显示
    /// </summary>
    public void ToggleDebugInfo()
    {
        showDebugInfo = !showDebugInfo;
    }
    
    /// <summary>
    /// 设置调试信息显示状态
    /// </summary>
    /// <param name="show">是否显示</param>
    public void SetDebugInfoVisible(bool show)
    {
        showDebugInfo = show;
    }
}
