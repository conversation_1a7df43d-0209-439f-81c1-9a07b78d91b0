{"name": "Unity.VisualScripting.Shared.Editor", "references": ["Unity.VisualScripting.Core.Editor", "Unity.VisualScripting.Flow.Editor", "Unity.VisualScripting.State.Editor", "Unity.VisualScripting.Core", "Unity.VisualScripting.Flow", "Unity.VisualScripting.State"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}