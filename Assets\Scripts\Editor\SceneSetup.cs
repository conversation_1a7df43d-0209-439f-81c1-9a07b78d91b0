using UnityEngine;
using UnityEditor;

public class SceneSetup : EditorWindow
{
    [MenuItem("Tools/创建测试场景")]
    public static void CreateTestScene()
    {
        Debug.Log("开始创建测试场景...");
        
        // 创建地面
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Cube);
        ground.name = "Ground";
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = new Vector3(20, 0.1f, 20);
        
        // 创建玩家
        GameObject player = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        player.name = "Player";
        player.tag = "Player";
        player.transform.position = new Vector3(0, 1, 0);
        
        // 移除碰撞器并添加CharacterController
        DestroyImmediate(player.GetComponent<Collider>());
        player.AddComponent<CharacterController>();
        player.AddComponent<TestMovement>();
        
        // 设置相机
        Camera cam = Camera.main;
        if (cam != null)
        {
            cam.transform.position = new Vector3(0, 5, -10);
            cam.transform.LookAt(player.transform);
        }
        
        Debug.Log("测试场景创建完成！");
        
        // 选中玩家
        Selection.activeGameObject = player;
    }
    
    [MenuItem("Tools/清理测试场景")]
    public static void CleanTestScene()
    {
        GameObject ground = GameObject.Find("Ground");
        GameObject player = GameObject.Find("Player");
        
        if (ground) DestroyImmediate(ground);
        if (player) DestroyImmediate(player);
        
        Debug.Log("测试场景已清理");
    }
}
