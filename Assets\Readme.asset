%YAML 1.1
%TAG !u! tag:yousandi.cn,2023:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fcf7219bab7fe46a1ad266029b2fee19, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  icon: {fileID: 2800000, guid: 727a75301c3d24613a3ebcec4a24c2c8, type: 3}
  title: URP Empty Template
  sections:
  - heading: Welcome to the Universal Render Pipeline
    text: This template includes the settings and assets you need to start creating with the Universal Render Pipeline.
    linkText: 
    url:
  - heading: URP Documentation
    text:
    linkText: Read more about URP
    url: https://docs.unity3d.com/Packages/com.unity.render-pipelines.universal@latest
  - heading: Forums
    text:
    linkText: Get answers and support
    url: https://forum.unity.com/forums/universal-render-pipeline.383/
  - heading: Report bugs
    text:
    linkText: Submit a report
    url: https://unity3d.com/unity/qa/bug-reporting
  loadedLayout: 1
