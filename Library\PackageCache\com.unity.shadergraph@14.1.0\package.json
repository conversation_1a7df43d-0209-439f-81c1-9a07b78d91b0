{"name": "com.unity.shadergraph", "description": "The Shader Graph package adds a visual Shader editing tool to Unity. You can use this tool to create Shaders in a visual way instead of writing code. Specific render pipelines can implement specific graph features. Currently, both the High Definition Rendering Pipeline and the Universal Rendering Pipeline support Shader Graph.", "version": "14.1.0", "unity": "2022.3", "displayName": "Shader Graph", "dependencies": {"com.unity.render-pipelines.core": "14.1.0", "com.unity.searcher": "4.9.2"}, "samples": [{"displayName": "Procedural Patterns", "description": "This collection of assets showcase various procedural techniques possible with Shader Graph. Use them in your project or edit them to create other procedural pattens. Patterns: Bacteria, Brick, Dots, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>oth Wave, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>chet, <PERSON>hirl, Zig Zag", "path": "Samples~/ProceduralPatterns"}, {"displayName": "Node Reference", "description": "This set of Shader Graph assets provides reference material for the nodes available in the Shader Graph node library. Each graph contains a description for a specific node, examples of how it can be used, and useful tips. Some example assets also show a break-down of the math that the node is doing. You can use these samples along with the documentation to learn more about the behavior of individual nodes.", "path": "Samples~/NodeReference"}, {"displayName": "Feature Examples", "description": "This set of assets provides examples for how to achieve specific features and effects in Shader Graph - such as parallax occlusion mapping, interior cube mapping, vertex animation, various types of UV projection, and more. While not intended to be used directly, these examples should help you learn how to achieve these specific effects in your own shaders.", "path": "Samples~/FeatureExamples"}, {"displayName": "Production Ready Shaders", "description": "This sample contains a collection of Shader Graph assets that are ready to be used in production.  The collection includes shaders for rocks, decals, water, terrain details, and many more.  It also includes post-process examples, weather effects, and shaders that behave similarly to the URP and HDRP Lit shaders.", "path": "Samples~/ProductionReady"}]}