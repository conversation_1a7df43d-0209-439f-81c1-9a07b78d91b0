using UnityEngine;

/// <summary>
/// 快速设置脚本 - 确保能正常工作的最简版本
/// </summary>
public class QuickSetup : MonoBehaviour
{
    void Start()
    {
        Debug.Log("QuickSetup 开始执行...");
        
        // 延迟执行，确保所有系统初始化完成
        Invoke("CreateTestScene", 0.1f);
    }
    
    void CreateTestScene()
    {
        Debug.Log("开始创建测试场景...");
        
        // 1. 创建地面
        CreateGround();
        
        // 2. 创建玩家
        CreatePlayer();
        
        // 3. 设置相机
        SetupCamera();
        
        Debug.Log("测试场景创建完成！使用WASD移动角色。");
    }
    
    void CreateGround()
    {
        // 删除已存在的地面
        GameObject existingGround = GameObject.Find("Ground");
        if (existingGround != null)
        {
            DestroyImmediate(existingGround);
        }
        
        // 创建新地面
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Cube);
        ground.name = "Ground";
        ground.transform.position = new Vector3(0, -0.5f, 0);
        ground.transform.localScale = new Vector3(20, 1, 20);
        
        // 设置颜色
        Renderer renderer = ground.GetComponent<Renderer>();
        Material material = new Material(Shader.Find("Standard"));
        material.color = new Color(0.5f, 0.5f, 0.5f); // 灰色
        renderer.material = material;
        
        Debug.Log("地面创建完成");
    }
    
    void CreatePlayer()
    {
        // 删除已存在的玩家
        GameObject existingPlayer = GameObject.Find("Player");
        if (existingPlayer != null)
        {
            DestroyImmediate(existingPlayer);
        }
        
        // 创建新玩家
        GameObject player = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        player.name = "Player";
        player.tag = "Player";
        player.transform.position = new Vector3(0, 1, 0);
        
        // 移除默认碰撞器
        Collider collider = player.GetComponent<Collider>();
        if (collider != null)
        {
            DestroyImmediate(collider);
        }
        
        // 添加CharacterController
        CharacterController controller = player.AddComponent<CharacterController>();
        controller.radius = 0.5f;
        controller.height = 2f;
        controller.center = new Vector3(0, 0, 0);
        
        // 添加移动脚本
        player.AddComponent<BasicPlayerMovement>();
        
        // 设置颜色
        Renderer renderer = player.GetComponent<Renderer>();
        Material material = new Material(Shader.Find("Standard"));
        material.color = Color.blue;
        renderer.material = material;
        
        Debug.Log("玩家创建完成");
    }
    
    void SetupCamera()
    {
        // 找到主相机
        Camera mainCamera = Camera.main;
        if (mainCamera == null)
        {
            // 如果没有主相机，创建一个
            GameObject cameraObj = new GameObject("Main Camera");
            mainCamera = cameraObj.AddComponent<Camera>();
            cameraObj.tag = "MainCamera";
        }
        
        // 设置相机位置
        mainCamera.transform.position = new Vector3(0, 5, -7);
        mainCamera.transform.LookAt(new Vector3(0, 1, 0));
        
        // 添加相机跟随脚本
        BasicCameraFollow cameraFollow = mainCamera.GetComponent<BasicCameraFollow>();
        if (cameraFollow == null)
        {
            cameraFollow = mainCamera.gameObject.AddComponent<BasicCameraFollow>();
        }
        
        Debug.Log("相机设置完成");
    }
    
    void Update()
    {
        // 按R键重新创建场景
        if (Input.GetKeyDown(KeyCode.R))
        {
            Debug.Log("按下R键，重新创建场景...");
            CreateTestScene();
        }
    }
}
