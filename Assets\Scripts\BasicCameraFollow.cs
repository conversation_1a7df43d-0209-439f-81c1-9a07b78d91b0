using UnityEngine;

/// <summary>
/// 基础相机跟随脚本
/// </summary>
public class BasicCameraFollow : MonoBehaviour
{
    public float followSpeed = 2f;
    public Vector3 offset = new Vector3(0, 5, -7);
    
    private Transform target;
    
    void Start()
    {
        // 查找玩家
        GameObject player = GameObject.FindWithTag("Player");
        if (player != null)
        {
            target = player.transform;
            Debug.Log("相机找到玩家目标");
        }
        else
        {
            Debug.LogWarning("相机没有找到玩家！");
        }
    }
    
    void LateUpdate()
    {
        if (target == null)
        {
            // 重新尝试查找玩家
            GameObject player = GameObject.FindWithTag("Player");
            if (player != null)
            {
                target = player.transform;
                Debug.Log("相机重新找到玩家目标");
            }
            return;
        }
        
        // 计算目标位置
        Vector3 targetPosition = target.position + offset;
        
        // 平滑移动到目标位置
        transform.position = Vector3.Lerp(transform.position, targetPosition, followSpeed * Time.deltaTime);
        
        // 看向玩家
        transform.LookAt(target.position + Vector3.up);
    }
}
