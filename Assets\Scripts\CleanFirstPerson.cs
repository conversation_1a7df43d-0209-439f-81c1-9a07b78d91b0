using UnityEngine;

public class CleanFirstPerson : MonoBehaviour
{
    public float speed = 5f;
    public float mouseSensitivity = 2f;
    
    private CharacterController controller;
    private Camera cam;
    private float xRotation = 0f;
    
    void Start()
    {
        // 添加CharacterController
        controller = gameObject.AddComponent<CharacterController>();
        
        // 创建相机
        GameObject cameraObj = new GameObject("Camera");
        cameraObj.transform.parent = transform;
        cameraObj.transform.localPosition = new Vector3(0, 1.8f, 0);
        cam = cameraObj.AddComponent<Camera>();
        
        // 创建地面
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.transform.localScale = new Vector3(10, 1, 10);
        
        // 锁定鼠标
        Cursor.lockState = CursorLockMode.Locked;
        
        Debug.Log("第一人称设置完成！WASD移动，鼠标视角，ESC解锁");
    }
    
    void Update()
    {
        // 鼠标视角
        if (Cursor.lockState == CursorLockMode.Locked)
        {
            float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
            float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;
            
            transform.Rotate(Vector3.up * mouseX);
            
            xRotation -= mouseY;
            xRotation = Mathf.Clamp(xRotation, -90f, 90f);
            cam.transform.localRotation = Quaternion.Euler(xRotation, 0f, 0f);
        }
        
        // 移动
        float x = Input.GetAxis("Horizontal");
        float z = Input.GetAxis("Vertical");
        
        Vector3 move = transform.right * x + transform.forward * z;
        controller.Move(move * speed * Time.deltaTime);
        
        // 重力
        controller.Move(Vector3.down * 9.81f * Time.deltaTime);
        
        // ESC解锁鼠标
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            Cursor.lockState = CursorLockMode.None;
        }
        
        // 点击锁定鼠标
        if (Input.GetMouseButtonDown(0))
        {
            Cursor.lockState = CursorLockMode.Locked;
        }
    }
}
