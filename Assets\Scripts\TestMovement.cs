using UnityEngine;

public class TestMovement : MonoBehaviour
{
    public float speed = 5f;
    
    void Update()
    {
        float x = Input.GetAxis("Horizontal") * speed * Time.deltaTime;
        float z = Input.GetAxis("Vertical") * speed * Time.deltaTime;
        
        transform.Translate(x, 0, z);
        
        if (Input.anyKey)
        {
            Debug.Log("移动中: " + transform.position);
        }
    }
}
