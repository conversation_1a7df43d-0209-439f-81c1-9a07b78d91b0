using UnityEngine;

/// <summary>
/// 最简单的第一人称控制器 - 无依赖，确保能编译
/// </summary>
public class SimpleFirstPerson : MonoBehaviour
{
    [Header("移动设置")]
    public float walkSpeed = 5f;
    public float runSpeed = 8f;
    public float jumpHeight = 2f;
    public float gravity = -9.81f;
    
    [Header("鼠标设置")]
    public float mouseSensitivity = 2f;
    public float maxLookAngle = 80f;
    
    // 私有变量
    private CharacterController characterController;
    private Camera playerCamera;
    private Vector3 velocity;
    private bool isGrounded;
    private float verticalRotation = 0;
    
    void Start()
    {
        Debug.Log("SimpleFirstPerson 开始初始化...");
        
        // 获取或添加CharacterController
        characterController = GetComponent<CharacterController>();
        if (characterController == null)
        {
            characterController = gameObject.AddComponent<CharacterController>();
            characterController.height = 2f;
            characterController.radius = 0.5f;
            characterController.center = new Vector3(0, 1f, 0);
        }
        
        // 创建相机
        CreateCamera();
        
        // 创建简单场景
        CreateSimpleScene();
        
        // 设置鼠标
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
        
        Debug.Log("SimpleFirstPerson 初始化完成！");
        Debug.Log("控制：WASD移动，鼠标视角，Shift跑步，空格跳跃，ESC解锁鼠标");
    }
    
    void CreateCamera()
    {
        // 查找现有相机
        playerCamera = GetComponentInChildren<Camera>();
        
        if (playerCamera == null)
        {
            // 创建新相机
            GameObject cameraObj = new GameObject("PlayerCamera");
            cameraObj.transform.SetParent(transform);
            cameraObj.transform.localPosition = new Vector3(0, 1.6f, 0);
            cameraObj.transform.localRotation = Quaternion.identity;
            
            playerCamera = cameraObj.AddComponent<Camera>();
            playerCamera.tag = "MainCamera";
            
            // 禁用场景中的其他相机
            Camera[] allCameras = FindObjectsOfType<Camera>();
            foreach (Camera cam in allCameras)
            {
                if (cam != playerCamera && cam.name == "Main Camera")
                {
                    cam.gameObject.SetActive(false);
                }
            }
        }
        
        Debug.Log("相机设置完成");
    }
    
    void CreateSimpleScene()
    {
        // 创建地面（如果不存在）
        if (GameObject.Find("Ground") == null)
        {
            GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
            ground.name = "Ground";
            ground.transform.position = Vector3.zero;
            ground.transform.localScale = new Vector3(10, 1, 10);
            
            // 设置材质
            Renderer renderer = ground.GetComponent<Renderer>();
            if (renderer != null)
            {
                Material material = new Material(Shader.Find("Standard"));
                material.color = new Color(0.5f, 0.5f, 0.5f);
                renderer.material = material;
            }
            
            Debug.Log("地面创建完成");
        }
        
        // 创建光源（如果不存在）
        if (FindObjectOfType<Light>() == null)
        {
            GameObject lightObj = new GameObject("Directional Light");
            Light light = lightObj.AddComponent<Light>();
            light.type = LightType.Directional;
            light.intensity = 1f;
            lightObj.transform.rotation = Quaternion.Euler(50, -30, 0);
            
            Debug.Log("光源创建完成");
        }
        
        // 设置玩家位置
        transform.position = new Vector3(0, 1, 0);
    }
    
    void Update()
    {
        HandleMouseLook();
        HandleMovement();
        HandleJump();
        HandleInput();
    }
    
    void HandleMouseLook()
    {
        if (Cursor.lockState != CursorLockMode.Locked) return;
        if (playerCamera == null) return;
        
        // 获取鼠标输入
        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;
        
        // 水平旋转（玩家身体）
        transform.Rotate(Vector3.up * mouseX);
        
        // 垂直旋转（相机）
        verticalRotation -= mouseY;
        verticalRotation = Mathf.Clamp(verticalRotation, -maxLookAngle, maxLookAngle);
        playerCamera.transform.localRotation = Quaternion.Euler(verticalRotation, 0, 0);
    }
    
    void HandleMovement()
    {
        if (characterController == null) return;
        
        // 检查是否在地面
        isGrounded = characterController.isGrounded;
        if (isGrounded && velocity.y < 0)
        {
            velocity.y = -2f;
        }
        
        // 获取输入
        float horizontal = Input.GetAxis("Horizontal");
        float vertical = Input.GetAxis("Vertical");
        
        // 计算移动方向
        Vector3 direction = transform.right * horizontal + transform.forward * vertical;
        direction = direction.normalized;
        
        // 确定速度
        float currentSpeed = Input.GetKey(KeyCode.LeftShift) ? runSpeed : walkSpeed;
        
        // 移动
        characterController.Move(direction * currentSpeed * Time.deltaTime);
        
        // 重力
        velocity.y += gravity * Time.deltaTime;
        characterController.Move(velocity * Time.deltaTime);
    }
    
    void HandleJump()
    {
        if (Input.GetButtonDown("Jump") && isGrounded)
        {
            velocity.y = Mathf.Sqrt(jumpHeight * -2f * gravity);
        }
    }
    
    void HandleInput()
    {
        // ESC解锁鼠标
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
        }
        
        // 点击重新锁定鼠标
        if (Input.GetMouseButtonDown(0) && Cursor.lockState == CursorLockMode.None)
        {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }
    }
}
