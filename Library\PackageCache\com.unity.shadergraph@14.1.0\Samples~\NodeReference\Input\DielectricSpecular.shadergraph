{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "af48ded8f1cb4113b9715a26c92d072c",
    "m_Properties": [],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "b4df0b02a8cf4d57bd916787131b80be"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "5b159ee53fe74789822c333ad187952d"
        },
        {
            "m_Id": "d6ef82a13c294319a1959f88219f74ff"
        },
        {
            "m_Id": "be17ca62f76e4c11a21fd9c365b9f34c"
        },
        {
            "m_Id": "9db26efae1f540af83c4e2e42b324c1e"
        },
        {
            "m_Id": "9e0ce1b0cc08455fa2f96d9b4d8a53b2"
        },
        {
            "m_Id": "b26b51bc402043a89956bba481941fca"
        },
        {
            "m_Id": "eb987b4ec0d34112aefd99f26f46be65"
        },
        {
            "m_Id": "e45a918ffa84489b954c8b3cc9946bc4"
        },
        {
            "m_Id": "c460bdde82b244e39cedc089ea37ff30"
        },
        {
            "m_Id": "f61b5512016c4c1ead9148052f7a0e08"
        },
        {
            "m_Id": "659827a0b29249d18111cf23bc00e9c0"
        },
        {
            "m_Id": "ce94f128e18e40548e2d4f9f53380e04"
        },
        {
            "m_Id": "1bcd7d06157e43e18a9693a33cfc8280"
        },
        {
            "m_Id": "0de8e16388ef488a81e6a786e5a9a209"
        },
        {
            "m_Id": "74b2f39f3bb04ad9bef8c49e4a1367a1"
        },
        {
            "m_Id": "c9646c73d4564c95bdca61886cf3c33b"
        },
        {
            "m_Id": "9c1f5bd7877d41f0a73742bd844c148b"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "903fd5ba140d42ce9c42e2c662f06746"
        },
        {
            "m_Id": "514b75ccbda547c69a8ffe8904e1bdb3"
        },
        {
            "m_Id": "9880d8310d85439289a287068dcf986e"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "0cf99fa70ea94263865c5694d292e223"
        },
        {
            "m_Id": "ca3eb07b837043ada38992dcf5c79246"
        },
        {
            "m_Id": "2f58a86fddd947418417db7a65d947fc"
        },
        {
            "m_Id": "50b0346809984523acec750709b44572"
        }
    ],
    "m_Edges": [],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "5b159ee53fe74789822c333ad187952d"
            },
            {
                "m_Id": "d6ef82a13c294319a1959f88219f74ff"
            },
            {
                "m_Id": "be17ca62f76e4c11a21fd9c365b9f34c"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "9db26efae1f540af83c4e2e42b324c1e"
            },
            {
                "m_Id": "ce94f128e18e40548e2d4f9f53380e04"
            },
            {
                "m_Id": "1bcd7d06157e43e18a9693a33cfc8280"
            },
            {
                "m_Id": "0de8e16388ef488a81e6a786e5a9a209"
            },
            {
                "m_Id": "74b2f39f3bb04ad9bef8c49e4a1367a1"
            },
            {
                "m_Id": "c9646c73d4564c95bdca61886cf3c33b"
            },
            {
                "m_Id": "9c1f5bd7877d41f0a73742bd844c148b"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_ActiveTargets": [
        {
            "m_Id": "ed0308bd5d644b028bb8070bcf4fc3fd"
        },
        {
            "m_Id": "d590c88d328d4e7aaf1f2b77b92cfe8c"
        },
        {
            "m_Id": "188cef7aa3f743dab73243538bb0cb1b"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0005a2aa56ed4aed9c589bc5e656ec74",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "07135ff052314537a3648238d1a9e52b",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "0cf99fa70ea94263865c5694d292e223",
    "m_Title": "Dielectric Specular Node",
    "m_Content": "The Dielectric Specular Node outputs a specular reflectance value for various material types depending on the selected settings.\n\nEvery type of material has a physically correct specular reflectance value, and using that value gives you the most realistic-looking results when creating that material in Unity. This node can help generate the correct value.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -787.0000610351563,
        "y": -209.50001525878907,
        "width": 268.5,
        "height": 167.50001525878907
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "0de8e16388ef488a81e6a786e5a9a209",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Emission",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "6b560e399f654ab6bb22a7b8db2033a2"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Emission"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0edeb38f032b4843b35f76fb0494e2ba",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "188cef7aa3f743dab73243538bb0cb1b",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "f5520c7ce1654b028573868158f298d8"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "1bcd7d06157e43e18a9693a33cfc8280",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.NormalTS",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "5c477684062d4affbd7d67b251242951"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.NormalTS"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "1f00b8dd0fff473bb9e2ac88abe4a10e"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "2c53a87481d94b13ae9fd95b63873e93",
    "m_Id": 0,
    "m_DisplayName": "Smoothness",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smoothness",
    "m_StageCapability": 2,
    "m_Value": 0.5,
    "m_DefaultValue": 0.5,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "2f58a86fddd947418417db7a65d947fc",
    "m_Title": "",
    "m_Content": "If you know the Index of Refraction value for the material type you're using, you can set the Material dropdown to Custom and then use the IOR slider to set the index of refraction. This will result in the correct specular value for that material type.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -565.0000610351563,
        "y": 39.500003814697269,
        "width": 200.00003051757813,
        "height": 129.5
    },
    "m_Group": {
        "m_Id": "514b75ccbda547c69a8ffe8904e1bdb3"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "50b0346809984523acec750709b44572",
    "m_Title": "",
    "m_Content": "For the material types shown here, you can simply select the type and get the exact specular value without needing any additional input.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1161.5001220703125,
        "y": 487.00006103515627,
        "width": 200.00006103515626,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "9880d8310d85439289a287068dcf986e"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "514b75ccbda547c69a8ffe8904e1bdb3",
    "m_Title": "Custom",
    "m_Position": {
        "x": -758.0000610351563,
        "y": -23.500022888183595
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "54546e8a67474bae8a0b137495eb19a0",
    "m_Id": 0,
    "m_DisplayName": "Alpha",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Alpha",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "5b159ee53fe74789822c333ad187952d",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "9f7d3b8fdcbd4507ae493922464ecd1f"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "5b38d016f53345499bfa2b9bdfb3a789",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "5c477684062d4affbd7d67b251242951",
    "m_Id": 0,
    "m_DisplayName": "Normal (Tangent Space)",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "NormalTS",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 3
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DielectricSpecularNode",
    "m_ObjectId": "659827a0b29249d18111cf23bc00e9c0",
    "m_Group": {
        "m_Id": "9880d8310d85439289a287068dcf986e"
    },
    "m_Name": "Dielectric Specular",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -665.0,
            "y": 304.0000305175781,
            "width": 153.99993896484376,
            "height": 176.49996948242188
        }
    },
    "m_Slots": [
        {
            "m_Id": "9fdcd863f4e9491eb91a5887a1fe554c"
        }
    ],
    "synonyms": [
        "reflectance"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Material": {
        "type": 4,
        "range": 0.5,
        "indexOfRefraction": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "6b560e399f654ab6bb22a7b8db2033a2",
    "m_Id": 0,
    "m_DisplayName": "Emission",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Emission",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 1,
    "m_DefaultColor": {
        "r": 0.0,
        "g": 0.0,
        "b": 0.0,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "74b2f39f3bb04ad9bef8c49e4a1367a1",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Occlusion",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "e8a6975415e044e2b0cca30693cda750"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Occlusion"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "78b3d023c6bd4236aa0c582e53b246be",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7a8905beb5474ae6af981c491efa2674",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7b0b447e761146caa21db036f1e508ea",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "7d88fcde3dd1410fb873273d32bf2cb8",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInUnlitSubTarget",
    "m_ObjectId": "86646665ca6642489a0af98fae75f0b7"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "8c9f3c781f14466cbd6593524be24947",
    "m_Id": 0,
    "m_DisplayName": "Specular Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Specular",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "8ed0e56e3ba74870931a5ae83766bde4",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "903fd5ba140d42ce9c42e2c662f06746",
    "m_Title": "Common",
    "m_Position": {
        "x": -1190.0001220703125,
        "y": -23.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "9880d8310d85439289a287068dcf986e",
    "m_Title": "Specific",
    "m_Position": {
        "x": -1190.5001220703125,
        "y": 245.50003051757813
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "9c1f5bd7877d41f0a73742bd844c148b",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Alpha",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "54546e8a67474bae8a0b137495eb19a0"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Alpha"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "9db26efae1f540af83c4e2e42b324c1e",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "ba48b9e6258f4805954b51544497af03"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DielectricSpecularNode",
    "m_ObjectId": "9e0ce1b0cc08455fa2f96d9b4d8a53b2",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Dielectric Specular",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -960.5000610351563,
            "y": -214.0,
            "width": 172.0,
            "height": 176.50003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "7b0b447e761146caa21db036f1e508ea"
        }
    ],
    "synonyms": [
        "reflectance"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Material": {
        "type": 0,
        "range": 0.5,
        "indexOfRefraction": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "9f7d3b8fdcbd4507ae493922464ecd1f",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "9fdcd863f4e9491eb91a5887a1fe554c",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "a7ed0e9f0f1a4e2baedcb28df434c584",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "af6df0107ffa442ca391ec6201481b3b",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DielectricSpecularNode",
    "m_ObjectId": "b26b51bc402043a89956bba481941fca",
    "m_Group": {
        "m_Id": "903fd5ba140d42ce9c42e2c662f06746"
    },
    "m_Name": "Dielectric Specular",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1165.0001220703125,
            "y": 35.499996185302737,
            "width": 172.00006103515626,
            "height": 176.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "07135ff052314537a3648238d1a9e52b"
        }
    ],
    "synonyms": [
        "reflectance"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Material": {
        "type": 0,
        "range": 0.5,
        "indexOfRefraction": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "b4df0b02a8cf4d57bd916787131b80be",
    "m_Name": "",
    "m_ChildObjectList": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "ba48b9e6258f4805954b51544497af03",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "be17ca62f76e4c11a21fd9c365b9f34c",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "af6df0107ffa442ca391ec6201481b3b"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DielectricSpecularNode",
    "m_ObjectId": "c460bdde82b244e39cedc089ea37ff30",
    "m_Group": {
        "m_Id": "9880d8310d85439289a287068dcf986e"
    },
    "m_Name": "Dielectric Specular",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -975.0000610351563,
            "y": 304.0000305175781,
            "width": 156.0,
            "height": 176.49996948242188
        }
    },
    "m_Slots": [
        {
            "m_Id": "78b3d023c6bd4236aa0c582e53b246be"
        }
    ],
    "synonyms": [
        "reflectance"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Material": {
        "type": 2,
        "range": 0.5,
        "indexOfRefraction": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "c9646c73d4564c95bdca61886cf3c33b",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Specular",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "8c9f3c781f14466cbd6593524be24947"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Specular"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "ca3eb07b837043ada38992dcf5c79246",
    "m_Title": "",
    "m_Content": "When Material is set to Common, the user can set the Specular value directly.\n\nThe vast majority of non-metallic materials have a reflectance of 4 percent - which can be acheived by setting the range slider to 0.5. Setting the slider to 1 gives you a 4.8 percent reflectance and setting the slider to 0.01 gives you a reflectance of 3.4 percent.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -988.5000610351563,
        "y": 40.000003814697269,
        "width": 200.0,
        "height": 180.00003051757813
    },
    "m_Group": {
        "m_Id": "903fd5ba140d42ce9c42e2c662f06746"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "ce94f128e18e40548e2d4f9f53380e04",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Smoothness",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "2c53a87481d94b13ae9fd95b63873e93"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Smoothness"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "d590c88d328d4e7aaf1f2b77b92cfe8c",
    "m_ActiveSubTarget": {
        "m_Id": "1f00b8dd0fff473bb9e2ac88abe4a10e"
    },
    "m_Datas": [
        {
            "m_Id": "8ed0e56e3ba74870931a5ae83766bde4"
        },
        {
            "m_Id": "5b38d016f53345499bfa2b9bdfb3a789"
        },
        {
            "m_Id": "7d88fcde3dd1410fb873273d32bf2cb8"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "d6ef82a13c294319a1959f88219f74ff",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "a7ed0e9f0f1a4e2baedcb28df434c584"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DielectricSpecularNode",
    "m_ObjectId": "e45a918ffa84489b954c8b3cc9946bc4",
    "m_Group": {
        "m_Id": "9880d8310d85439289a287068dcf986e"
    },
    "m_Name": "Dielectric Specular",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1165.5001220703125,
            "y": 304.0000305175781,
            "width": 190.50006103515626,
            "height": 176.49996948242188
        }
    },
    "m_Slots": [
        {
            "m_Id": "0005a2aa56ed4aed9c589bc5e656ec74"
        }
    ],
    "synonyms": [
        "reflectance"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Material": {
        "type": 1,
        "range": 0.5,
        "indexOfRefraction": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e8a6975415e044e2b0cca30693cda750",
    "m_Id": 0,
    "m_DisplayName": "Ambient Occlusion",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Occlusion",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DielectricSpecularNode",
    "m_ObjectId": "eb987b4ec0d34112aefd99f26f46be65",
    "m_Group": {
        "m_Id": "514b75ccbda547c69a8ffe8904e1bdb3"
    },
    "m_Name": "Dielectric Specular",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -733.0000610351563,
            "y": 34.999977111816409,
            "width": 165.5,
            "height": 176.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "7a8905beb5474ae6af981c491efa2674"
        }
    ],
    "synonyms": [
        "reflectance"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Material": {
        "type": 5,
        "range": 0.5,
        "indexOfRefraction": 1.0
    }
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "ed0308bd5d644b028bb8070bcf4fc3fd",
    "m_ActiveSubTarget": {
        "m_Id": "86646665ca6642489a0af98fae75f0b7"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalLitSubTarget",
    "m_ObjectId": "f5520c7ce1654b028573868158f298d8",
    "m_WorkflowMode": 0,
    "m_NormalDropOffSpace": 0,
    "m_ClearCoat": false,
    "m_BlendModePreserveSpecular": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DielectricSpecularNode",
    "m_ObjectId": "f61b5512016c4c1ead9148052f7a0e08",
    "m_Group": {
        "m_Id": "9880d8310d85439289a287068dcf986e"
    },
    "m_Name": "Dielectric Specular",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -819.0000610351563,
            "y": 304.0000305175781,
            "width": 154.00006103515626,
            "height": 176.49996948242188
        }
    },
    "m_Slots": [
        {
            "m_Id": "0edeb38f032b4843b35f76fb0494e2ba"
        }
    ],
    "synonyms": [
        "reflectance"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Material": {
        "type": 3,
        "range": 0.5,
        "indexOfRefraction": 1.0
    }
}

