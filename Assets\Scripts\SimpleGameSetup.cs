using UnityEngine;

/// <summary>
/// 简化版游戏设置脚本
/// </summary>
public class SimpleGameSetup : MonoBehaviour
{
    public bool autoSetup = true;
    
    void Start()
    {
        if (autoSetup)
        {
            SetupGame();
        }
    }
    
    public void SetupGame()
    {
        CreateGround();
        CreatePlayer();
        SetupCamera();
        Debug.Log("简化测试环境设置完成！");
    }
    
    void CreateGround()
    {
        GameObject ground = GameObject.Find("Ground");
        if (ground == null)
        {
            ground = GameObject.CreatePrimitive(PrimitiveType.Cube);
            ground.name = "Ground";
            ground.transform.position = Vector3.zero;
            ground.transform.localScale = new Vector3(20, 0.1f, 20);
        }
    }
    
    void CreatePlayer()
    {
        GameObject player = GameObject.Find("Player");
        if (player == null)
        {
            player = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            player.name = "Player";
            player.tag = "Player";
            player.transform.position = Vector3.up;
            
            // 移除默认碰撞器
            Collider col = player.GetComponent<Collider>();
            if (col != null)
            {
                DestroyImmediate(col);
            }
            
            // 添加控制器
            player.AddComponent<SimplePlayerController>();
        }
    }
    
    void SetupCamera()
    {
        Camera cam = Camera.main;
        if (cam == null)
        {
            GameObject cameraObj = new GameObject("Main Camera");
            cam = cameraObj.AddComponent<Camera>();
            cameraObj.tag = "MainCamera";
        }
        
        SimpleCameraFollow follow = cam.GetComponent<SimpleCameraFollow>();
        if (follow == null)
        {
            follow = cam.gameObject.AddComponent<SimpleCameraFollow>();
        }
        
        GameObject player = GameObject.FindWithTag("Player");
        if (player != null)
        {
            follow.target = player.transform;
        }
    }
}
