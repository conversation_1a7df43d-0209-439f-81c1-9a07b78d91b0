using UnityEngine;

/// <summary>
/// 专门修复相机问题的脚本
/// </summary>
public class FixCamera : MonoBehaviour
{
    void Start()
    {
        Debug.Log("=== 开始修复相机问题 ===");
        
        // 立即禁用所有现有相机
        DisableAllCameras();
        
        // 创建完整的第一人称场景
        CreateCompleteFirstPersonScene();
        
        Debug.Log("=== 相机修复完成！===");
    }
    
    void DisableAllCameras()
    {
        Camera[] allCameras = FindObjectsOfType<Camera>();
        Debug.Log($"找到 {allCameras.Length} 个相机");
        
        foreach (Camera cam in allCameras)
        {
            cam.gameObject.SetActive(false);
            Debug.Log($"禁用相机: {cam.name}");
        }
    }
    
    void CreateCompleteFirstPersonScene()
    {
        // 清理现有对象
        CleanupExistingObjects();
        
        // 创建地面
        CreateGround();
        
        // 创建第一人称玩家（包含相机）
        CreateFirstPersonPlayer();
        
        // 创建光源
        CreateLight();
    }
    
    void CleanupExistingObjects()
    {
        string[] objectsToClean = { "Ground", "Player", "FPS_Ground", "FPS_Player", "FPS_Light" };
        
        foreach (string objName in objectsToClean)
        {
            GameObject obj = GameObject.Find(objName);
            if (obj != null)
            {
                DestroyImmediate(obj);
                Debug.Log($"清理对象: {objName}");
            }
        }
    }
    
    void CreateGround()
    {
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "FPS_Ground";
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = new Vector3(10, 1, 10);
        
        Renderer renderer = ground.GetComponent<Renderer>();
        Material mat = new Material(Shader.Find("Standard"));
        mat.color = new Color(0.7f, 0.7f, 0.7f);
        renderer.material = mat;
        
        Debug.Log("✓ 地面创建完成");
    }
    
    void CreateFirstPersonPlayer()
    {
        // 创建玩家根对象
        GameObject player = new GameObject("FPS_Player");
        player.transform.position = new Vector3(0, 1, 0);
        
        // 添加CharacterController
        CharacterController controller = player.AddComponent<CharacterController>();
        controller.height = 2f;
        controller.radius = 0.5f;
        controller.center = new Vector3(0, 1f, 0);
        
        // 创建第一人称相机
        GameObject cameraObj = new GameObject("FirstPersonCamera");
        cameraObj.transform.SetParent(player.transform);
        cameraObj.transform.localPosition = new Vector3(0, 1.6f, 0);
        cameraObj.transform.localRotation = Quaternion.identity;
        
        Camera fpCamera = cameraObj.AddComponent<Camera>();
        fpCamera.tag = "MainCamera";
        fpCamera.fieldOfView = 60f;
        
        // 添加第一人称控制脚本
        SimpleFPSController fpsController = player.AddComponent<SimpleFPSController>();
        fpsController.playerCamera = fpCamera;
        
        Debug.Log("✓ 第一人称玩家和相机创建完成");
    }
    
    void CreateLight()
    {
        GameObject lightObj = new GameObject("FPS_Light");
        Light light = lightObj.AddComponent<Light>();
        light.type = LightType.Directional;
        light.intensity = 1f;
        lightObj.transform.rotation = Quaternion.Euler(50, -30, 0);
        
        Debug.Log("✓ 光源创建完成");
    }
}

/// <summary>
/// 简化的第一人称控制器
/// </summary>
public class SimpleFPSController : MonoBehaviour
{
    public float moveSpeed = 5f;
    public float mouseSensitivity = 2f;
    public float maxLookAngle = 80f;
    
    public Camera playerCamera;
    
    private CharacterController controller;
    private Vector3 velocity;
    private float verticalRotation = 0f;
    
    void Start()
    {
        controller = GetComponent<CharacterController>();
        
        // 锁定鼠标
        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
        
        Debug.Log("第一人称控制器启动完成！");
        Debug.Log("控制：WASD移动，鼠标视角，ESC解锁鼠标");
    }
    
    void Update()
    {
        HandleMouseLook();
        HandleMovement();
        HandleInput();
    }
    
    void HandleMouseLook()
    {
        if (Cursor.lockState != CursorLockMode.Locked || playerCamera == null) return;
        
        float mouseX = Input.GetAxis("Mouse X") * mouseSensitivity;
        float mouseY = Input.GetAxis("Mouse Y") * mouseSensitivity;
        
        // 水平旋转（玩家身体）
        transform.Rotate(Vector3.up * mouseX);
        
        // 垂直旋转（相机）
        verticalRotation -= mouseY;
        verticalRotation = Mathf.Clamp(verticalRotation, -maxLookAngle, maxLookAngle);
        playerCamera.transform.localRotation = Quaternion.Euler(verticalRotation, 0, 0);
    }
    
    void HandleMovement()
    {
        if (controller == null) return;
        
        // 检查地面
        bool isGrounded = controller.isGrounded;
        if (isGrounded && velocity.y < 0)
        {
            velocity.y = -2f;
        }
        
        // 获取输入
        float horizontal = Input.GetAxis("Horizontal");
        float vertical = Input.GetAxis("Vertical");
        
        // 计算移动方向
        Vector3 direction = transform.right * horizontal + transform.forward * vertical;
        direction = direction.normalized;
        
        // 移动
        controller.Move(direction * moveSpeed * Time.deltaTime);
        
        // 重力
        velocity.y += -9.81f * Time.deltaTime;
        controller.Move(velocity * Time.deltaTime);
    }
    
    void HandleInput()
    {
        // ESC解锁鼠标
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
            Debug.Log("鼠标已解锁");
        }
        
        // 点击重新锁定鼠标
        if (Input.GetMouseButtonDown(0) && Cursor.lockState == CursorLockMode.None)
        {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
            Debug.Log("鼠标已锁定");
        }
    }
}
