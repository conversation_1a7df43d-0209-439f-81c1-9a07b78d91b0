# 🚀 Unity 角色移动测试 - 设置说明

## 问题解决方案

如果你看到空白场景且WASD无效，请按以下步骤操作：

## 📋 快速设置步骤

### 1. 创建设置对象
1. 在Unity中打开SampleScene场景
2. 在Hierarchy面板中右键点击
3. 选择 "Create Empty"
4. 将新对象重命名为 "GameSetup"

### 2. 添加设置脚本
1. 选中GameSetup对象
2. 在Inspector面板点击 "Add Component"
3. 搜索并添加 "QuickSetup" 脚本

### 3. 运行测试
1. 点击Play按钮
2. 查看Console面板的输出信息
3. 应该会看到：
   - "QuickSetup 开始执行..."
   - "开始创建测试场景..."
   - "地面创建完成"
   - "玩家创建完成"
   - "相机设置完成"
   - "测试场景创建完成！使用WASD移动角色。"

### 4. 测试移动
- **W**: 向前移动
- **A**: 向左移动
- **S**: 向后移动
- **D**: 向右移动
- **R**: 重新创建场景

## 🔍 故障排除

### 如果仍然没有反应：

1. **检查Console面板**：
   - Window → General → Console
   - 查看是否有错误信息

2. **手动检查Hierarchy**：
   - 运行后应该看到：
     - Ground (灰色立方体)
     - Player (蓝色胶囊)
     - Main Camera

3. **检查Player标签**：
   - 选中Player对象
   - 确认Tag设置为"Player"

4. **检查相机位置**：
   - 选中Main Camera
   - 确认Position大约为(0, 5, -7)

### 如果脚本没有执行：

1. **确认脚本已添加**：
   - 选中GameSetup对象
   - 在Inspector中应该看到QuickSetup脚本

2. **检查脚本编译**：
   - 查看Console是否有编译错误
   - 确保所有脚本都没有红色错误

3. **重新创建**：
   - 停止播放
   - 删除GameSetup对象
   - 重新按步骤1-3操作

## 🎮 预期效果

正确设置后，你应该看到：
- 一个灰色的大地面
- 一个蓝色的胶囊形角色
- 相机从后上方跟随角色
- 使用WASD可以控制角色移动
- 角色会面向移动方向
- Console中显示移动调试信息

## 🔧 高级选项

### 调整参数：
在BasicPlayerMovement脚本中可以调整：
- `moveSpeed`: 移动速度
- `rotationSpeed`: 旋转速度

在BasicCameraFollow脚本中可以调整：
- `followSpeed`: 跟随速度
- `offset`: 相机偏移位置

### 重新创建场景：
- 运行时按R键可以重新创建整个测试场景

## ❗ 重要提示

1. 确保使用SampleScene场景
2. 必须添加QuickSetup脚本到场景中的对象
3. 运行前检查Console面板确保没有编译错误
4. 如果移动没反应，检查Player对象是否有BasicPlayerMovement脚本
