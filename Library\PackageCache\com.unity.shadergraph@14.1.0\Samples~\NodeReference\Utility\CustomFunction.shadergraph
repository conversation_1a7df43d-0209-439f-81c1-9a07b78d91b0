{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "6ede961201ec4e039938d90323345d3a",
    "m_Properties": [],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "5090fb8e6f754c109fea9d0dd88f5cc7"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "7bf1166be7484e618d1655ab47cc168b"
        },
        {
            "m_Id": "46d8a6e8124446e08f11ca6e0b9ed890"
        },
        {
            "m_Id": "409b2b8a091d48d693c5d89cb4559fc5"
        },
        {
            "m_Id": "0b8b05b8ca914d99b7c2aa4472897680"
        },
        {
            "m_Id": "651fa2bcb16648b999463bb9c61a16c4"
        },
        {
            "m_Id": "a3eb0cbef0084d12a8727dfea2c7ca00"
        },
        {
            "m_Id": "5f8dca7b7e74420ba1e1b148c954adac"
        },
        {
            "m_Id": "a78408b27ee4459685acc8000346fd8a"
        },
        {
            "m_Id": "0841a2cb1ecb4025b76ddf1e24bd9f2f"
        },
        {
            "m_Id": "8bf6c322212f4f9c9d64bab7ea920dd8"
        },
        {
            "m_Id": "dd3bb329b97d47db90312c1bf429329e"
        },
        {
            "m_Id": "1876f3b6d77c4f1bbd7deb003702f5b6"
        },
        {
            "m_Id": "efc5fbb2eedb4fe786d4eddf8b763cf4"
        },
        {
            "m_Id": "cc1f56ccd944497e9e88ccd366b5b05d"
        },
        {
            "m_Id": "3239d5a5280642098f132d653c2f7357"
        },
        {
            "m_Id": "9991247fe903492ca56e8450e1276421"
        },
        {
            "m_Id": "37055e61fe3b41d4b8d06b5a92b5703c"
        },
        {
            "m_Id": "aecca882c05d4abba454f3cb5cf8d6c8"
        },
        {
            "m_Id": "7de828f883394fd1924d4a69baaf354c"
        },
        {
            "m_Id": "707fa6983c9e45ea8b38fa94881c78af"
        },
        {
            "m_Id": "31a298be6d7b418b8b1d2ccd35caf01a"
        },
        {
            "m_Id": "f00171cda8e249de8c0bd5d7b818dc5f"
        },
        {
            "m_Id": "a9076e381e6c417480382d7594819a28"
        },
        {
            "m_Id": "470d809a61c646f1a762bd041db7a976"
        },
        {
            "m_Id": "6334de2fc93e462283f01c2ea14718df"
        },
        {
            "m_Id": "857c6e998a61493685609ce67d82f7d0"
        },
        {
            "m_Id": "7ba43c2903a746678bbdc909495591d0"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "590ee3e0b3644cb580928a2787074b7c"
        },
        {
            "m_Id": "587f4a8a5290432a99a318c534102216"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "a969794fd7694e05b735f5d87a7c5368"
        },
        {
            "m_Id": "4eb09892c0bf4d909d45262433aa9920"
        },
        {
            "m_Id": "247130bf3875444f81b20436820239fd"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "0841a2cb1ecb4025b76ddf1e24bd9f2f"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "470d809a61c646f1a762bd041db7a976"
                },
                "m_SlotId": 3
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "1876f3b6d77c4f1bbd7deb003702f5b6"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "9991247fe903492ca56e8450e1276421"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "31a298be6d7b418b8b1d2ccd35caf01a"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "470d809a61c646f1a762bd041db7a976"
                },
                "m_SlotId": 6
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "31a298be6d7b418b8b1d2ccd35caf01a"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "f00171cda8e249de8c0bd5d7b818dc5f"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "3239d5a5280642098f132d653c2f7357"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "9991247fe903492ca56e8450e1276421"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "37055e61fe3b41d4b8d06b5a92b5703c"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "470d809a61c646f1a762bd041db7a976"
                },
                "m_SlotId": 10
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "5f8dca7b7e74420ba1e1b148c954adac"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "470d809a61c646f1a762bd041db7a976"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "707fa6983c9e45ea8b38fa94881c78af"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "470d809a61c646f1a762bd041db7a976"
                },
                "m_SlotId": 12
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "7de828f883394fd1924d4a69baaf354c"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "707fa6983c9e45ea8b38fa94881c78af"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "8bf6c322212f4f9c9d64bab7ea920dd8"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "470d809a61c646f1a762bd041db7a976"
                },
                "m_SlotId": 5
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "9991247fe903492ca56e8450e1276421"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "37055e61fe3b41d4b8d06b5a92b5703c"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "a3eb0cbef0084d12a8727dfea2c7ca00"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "470d809a61c646f1a762bd041db7a976"
                },
                "m_SlotId": 9
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "a78408b27ee4459685acc8000346fd8a"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "470d809a61c646f1a762bd041db7a976"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "a9076e381e6c417480382d7594819a28"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3239d5a5280642098f132d653c2f7357"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "aecca882c05d4abba454f3cb5cf8d6c8"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "470d809a61c646f1a762bd041db7a976"
                },
                "m_SlotId": 11
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "cc1f56ccd944497e9e88ccd366b5b05d"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3239d5a5280642098f132d653c2f7357"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "cc1f56ccd944497e9e88ccd366b5b05d"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "470d809a61c646f1a762bd041db7a976"
                },
                "m_SlotId": 13
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "dd3bb329b97d47db90312c1bf429329e"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "470d809a61c646f1a762bd041db7a976"
                },
                "m_SlotId": 8
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "efc5fbb2eedb4fe786d4eddf8b763cf4"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "7de828f883394fd1924d4a69baaf354c"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "efc5fbb2eedb4fe786d4eddf8b763cf4"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "cc1f56ccd944497e9e88ccd366b5b05d"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "efc5fbb2eedb4fe786d4eddf8b763cf4"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "7de828f883394fd1924d4a69baaf354c"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "efc5fbb2eedb4fe786d4eddf8b763cf4"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "cc1f56ccd944497e9e88ccd366b5b05d"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "f00171cda8e249de8c0bd5d7b818dc5f"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "a9076e381e6c417480382d7594819a28"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "f00171cda8e249de8c0bd5d7b818dc5f"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "a9076e381e6c417480382d7594819a28"
                },
                "m_SlotId": 2
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "7bf1166be7484e618d1655ab47cc168b"
            },
            {
                "m_Id": "46d8a6e8124446e08f11ca6e0b9ed890"
            },
            {
                "m_Id": "409b2b8a091d48d693c5d89cb4559fc5"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "0b8b05b8ca914d99b7c2aa4472897680"
            },
            {
                "m_Id": "857c6e998a61493685609ce67d82f7d0"
            },
            {
                "m_Id": "7ba43c2903a746678bbdc909495591d0"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_ActiveTargets": [
        {
            "m_Id": "5468ac27d6574da593d00428fddce641"
        },
        {
            "m_Id": "c14afcd87ebe46a8a770ba66c3b40cde"
        },
        {
            "m_Id": "54bb74188f1b4f1ebf5c6024d74e7325"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "017e23d345954753966e9680942fdb60",
    "m_Id": 2,
    "m_DisplayName": "Y",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Y",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "Y"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "020769ddd3ff4228bed6d489da7e0a68",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1Node",
    "m_ObjectId": "0841a2cb1ecb4025b76ddf1e24bd9f2f",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "Float",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -760.0001220703125,
            "y": 572.0000610351563,
            "width": 125.49993896484375,
            "height": 77.00006103515625
        }
    },
    "m_Slots": [
        {
            "m_Id": "2d89aa6d130f443586b412ece98c41ca"
        },
        {
            "m_Id": "948e574c775a468fbde36d6075ed8ae2"
        }
    ],
    "synonyms": [
        "Vector 1",
        "1",
        "v1",
        "vec1",
        "scalar"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": 0.0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "0b8b05b8ca914d99b7c2aa4472897680",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "12f2c2097ee14c7ba3ea5cd3570e9d26"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0c4cf88bc14b4d29bd2045f0e85952f6",
    "m_Id": 1,
    "m_DisplayName": "Blurriness",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Blurriness",
    "m_StageCapability": 3,
    "m_Value": 0.05000000074505806,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "1298be9edda14f1e9e213a0fe4f0b5f5",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "12f2c2097ee14c7ba3ea5cd3570e9d26",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInUnlitSubTarget",
    "m_ObjectId": "14a5c917c8ca4236a2338d2e39a7ae3d"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ScreenPositionNode",
    "m_ObjectId": "1876f3b6d77c4f1bbd7deb003702f5b6",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "Screen Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1090.000244140625,
            "y": 223.00001525878907,
            "width": 145.0001220703125,
            "height": 128.5000457763672
        }
    },
    "m_Slots": [
        {
            "m_Id": "b739075bac224e6798a90c1f4c2f4633"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_ScreenSpaceType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "1af9c50d75b84673a25a893068c244ff",
    "m_Id": 2,
    "m_DisplayName": "Height",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Height",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "22dfd2cc84ae439f9e5391c6aec8fa4f",
    "m_Id": 5,
    "m_DisplayName": "BlurMultiplier",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BlurMultiplier",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "247130bf3875444f81b20436820239fd",
    "m_Title": "",
    "m_Content": "In this basic example, we've created a float In port and a float Out port and then set the HLSL code to be Out = In; - so it's just passing through the input value to the output.  See the Graph Inspector for how this is done.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1837.5001220703125,
        "y": 168.50001525878907,
        "width": 200.0,
        "height": 123.50001525878906
    },
    "m_Group": {
        "m_Id": "587f4a8a5290432a99a318c534102216"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "27b79a72632746e48c7eefeeab428cb0",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "2d89aa6d130f443586b412ece98c41ca",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 4.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DAssetNode",
    "m_ObjectId": "31a298be6d7b418b8b1d2ccd35caf01a",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "Texture 2D Asset",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1568.************,
            "y": 521.5000610351563,
            "width": 143.5,
            "height": 106.00006103515625
        }
    },
    "m_Slots": [
        {
            "m_Id": "521327d320ce4d2da8813142b23087ce"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"cfaf770d6375fa84fa5aa5309688d155\",\"type\":3}}",
        "m_Guid": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DivideNode",
    "m_ObjectId": "3239d5a5280642098f132d653c2f7357",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "Divide",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1074.0001220703125,
            "y": 122.0000228881836,
            "width": 129.0,
            "height": 117.99999237060547
        }
    },
    "m_Slots": [
        {
            "m_Id": "50c9d8ae67f04354aa86e3d0ec732b1c"
        },
        {
            "m_Id": "3bd7b77b58e8402fa1b048391664ee41"
        },
        {
            "m_Id": "c720c9e4ccfe46418c3bfbbb89da1c8a"
        }
    ],
    "synonyms": [
        "division",
        "divided by"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "33de0718550a4118bb7452045af53d87",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "34a03a31a8964f4881f5d002a63e651d",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "36121af362f04f4fb8702547b546de58",
    "m_Id": 11,
    "m_DisplayName": "Hack",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Hack",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.FractionNode",
    "m_ObjectId": "37055e61fe3b41d4b8d06b5a92b5703c",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "Fraction",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -790.5001220703125,
            "y": 200.00003051757813,
            "width": 130.50006103515626,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "c99bc7915a1743e8abfad84b05ee0777"
        },
        {
            "m_Id": "d35f4f6e26d044649d18c68ddca18ac5"
        }
    ],
    "synonyms": [
        "remainder"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "37d3baf384804211919e91fdefc2c80c",
    "m_Id": 13,
    "m_DisplayName": "SceneSize",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "SceneSize",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "3aa0f891c0fa4f1691ca174e68d9274b",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "3bd7b77b58e8402fa1b048391664ee41",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 64.0,
        "y": 64.0,
        "z": 2.0,
        "w": 2.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "3f1c4076faf043a5adc206eda4d8c2fa",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"instanceID\":0}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "409b2b8a091d48d693c5d89cb4559fc5",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "fbb76735c8f541b790e2bd2f33a9ff16"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "43b694412f3b4051ad76e240a8139d84",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "4403773f925142888d9ccefde293c62c",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ScreenPositionMaterialSlot",
    "m_ObjectId": "443a4122451045ffb7cfb247d4142d6b",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": [],
    "m_ScreenSpaceType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "44f2536efb364e8db6fafa129c0747bc",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "46d8a6e8124446e08f11ca6e0b9ed890",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "4403773f925142888d9ccefde293c62c"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.CustomFunctionNode",
    "m_ObjectId": "470d809a61c646f1a762bd041db7a976",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "SpiralBlurCustomFunction (Custom Function)",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -549.5001831054688,
            "y": 328.0000305175781,
            "width": 301.0001220703125,
            "height": 334.0000915527344
        }
    },
    "m_Slots": [
        {
            "m_Id": "bb6ed326c2304c0ba53c7a347dcf85a0"
        },
        {
            "m_Id": "ed44229b3d044a2cae0f48839f3513eb"
        },
        {
            "m_Id": "37d3baf384804211919e91fdefc2c80c"
        },
        {
            "m_Id": "f917db46ff7d4a0e9d66302d30c57761"
        },
        {
            "m_Id": "0c4cf88bc14b4d29bd2045f0e85952f6"
        },
        {
            "m_Id": "d5d59a0516344bdab2db4ef77fc5d163"
        },
        {
            "m_Id": "fd7e4413a23541c7a92e62dd9adf33aa"
        },
        {
            "m_Id": "22dfd2cc84ae439f9e5391c6aec8fa4f"
        },
        {
            "m_Id": "5a73a7ea4b1b4231acca350510e9b794"
        },
        {
            "m_Id": "9c73d99ccf184de2b607bb050d84912a"
        },
        {
            "m_Id": "36121af362f04f4fb8702547b546de58"
        },
        {
            "m_Id": "b11109c991c94ab692e83cff8e329a66"
        }
    ],
    "synonyms": [
        "code",
        "HLSL"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SourceType": 1,
    "m_FunctionName": "SpiralBlurCustomFunction",
    "m_FunctionSource": "",
    "m_FunctionBody": "float2 NextUV = ScreenPosition;\r\nfloat StepSize = Blurriness / (int) Cycles;\r\nfloat CurDistance=0;\r\nfloat2 CurOffset;\r\r\nfloat Substep;\nfloat Noise = SAMPLE_TEXTURE2D(Tex.tex,Tex.samplerstate,ScenePixels).w *2-1;\n\n//it's a little bit faster using this procedural hash instead of the noise texture\n//but the results aren't as nice\nuint2 v = (uint2) (int2) round(ScreenPosition*SceneSize*2);\r\nv.y ^= 1103515245U;\r\nv.x += v.y;\r\nv.x *= v.y;\r\nv.x ^= v.x >> 5u;\r\nv.x *= 0x27d4eb2du;\r\n//float Noise = (v.x * (1.0 / float(0xffffffff)))*2-1 ;\n\r\r\nif (Cycles<1)\r\n{\r\n\tOut = SHADERGRAPH_SAMPLE_SCENE_COLOR(NextUV).xyz;\r\n}\r\nelse\r\n{\r\n\tfor (int i = 0; i < (int) Cycles; i++)\r\n\t{\r\n\t\tfor (int j = 0; j < (int) SamplesPerCycle; j++)\r\n\t\t{\r\n\t\t\tsincos(6.283185*((Noise+Substep) / SamplesPerCycle), CurOffset.y, CurOffset.x);\r\n\t\t\tCurOffset *=BlurMultiplier;\n                              NextUV = ScreenPosition + (CurOffset * (CurDistance+(Noise*StepSize))* WidthHeightRatio);\r\r\n\t\t\tOut += SHADERGRAPH_SAMPLE_SCENE_COLOR(NextUV).xyz;\r\n\t\t\tSubstep++;\r\n\t\t}\r\n\t\tCurDistance+=StepSize;\r\n\t\tSubstep+=RadialOffset;\r\r\n\t}\r\n\tOut = Out / ((int)Cycles*(int)SamplesPerCycle);\r\n}"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "4eb09892c0bf4d909d45262433aa9920",
    "m_Title": "",
    "m_Content": "In this example, we use the Custom Function node to sample from the Scene Color texture multiple times in a spiral pattern and then average all of the samples together. The result is a frosted glass effect.\n\nThe Custom Function node allows us to do nested loops - something that wouldn't otherwise be possible in Shader Graph.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -512.5000610351563,
        "y": 99.50001525878906,
        "width": 200.0,
        "height": 185.00001525878907
    },
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "5090fb8e6f754c109fea9d0dd88f5cc7",
    "m_Name": "",
    "m_ChildObjectList": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "50c9d8ae67f04354aa86e3d0ec732b1c",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DMaterialSlot",
    "m_ObjectId": "521327d320ce4d2da8813142b23087ce",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "5468ac27d6574da593d00428fddce641",
    "m_ActiveSubTarget": {
        "m_Id": "14a5c917c8ca4236a2338d2e39a7ae3d"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "54bb74188f1b4f1ebf5c6024d74e7325",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "95e6edf6bfa1454ca0673681bc8ac466"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "587bb630f14943f39d7bb860b9b867f4",
    "m_Id": 0,
    "m_DisplayName": "Width",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Width",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "587f4a8a5290432a99a318c534102216",
    "m_Title": "Basic Example",
    "m_Position": {
        "x": -1862.************,
        "y": 4.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "590ee3e0b3644cb580928a2787074b7c",
    "m_Title": "Frosted Glass Example",
    "m_Position": {
        "x": -1593.************,
        "y": 4.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "5a73a7ea4b1b4231acca350510e9b794",
    "m_Id": 6,
    "m_DisplayName": "Tex",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tex",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"81200413a40918d4d8702e94db29911c\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1Node",
    "m_ObjectId": "5f8dca7b7e74420ba1e1b148c954adac",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "Float",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -760.0001220703125,
            "y": 418.0000915527344,
            "width": 125.49993896484375,
            "height": 76.99996948242188
        }
    },
    "m_Slots": [
        {
            "m_Id": "fc17ead17bb04dea9e7b9cbd6b99986e"
        },
        {
            "m_Id": "ae47cf8fbc9a4c51b291b41df64136ff"
        }
    ],
    "synonyms": [
        "Vector 1",
        "1",
        "v1",
        "vec1",
        "scalar"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": 0.0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "61b009fe74c0483c8c8745d46123da7f",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.CustomFunctionNode",
    "m_ObjectId": "6334de2fc93e462283f01c2ea14718df",
    "m_Group": {
        "m_Id": "587f4a8a5290432a99a318c534102216"
    },
    "m_Name": "Simple (Custom Function)",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1835.************,
            "y": 62.50003433227539,
            "width": 192.5001220703125,
            "height": 94.00001525878906
        }
    },
    "m_Slots": [
        {
            "m_Id": "7730fcc93a2a4aed8f2704507b6e2dae"
        },
        {
            "m_Id": "cfbf59fafbd04045826a367f33c88b63"
        }
    ],
    "synonyms": [
        "code",
        "HLSL"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SourceType": 1,
    "m_FunctionName": "Simple",
    "m_FunctionSource": "",
    "m_FunctionBody": "Out = In;"
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.CustomFunctionNode",
    "m_ObjectId": "651fa2bcb16648b999463bb9c61a16c4",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Custom Function",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1057.************,
            "y": -176.5,
            "width": 142.0001220703125,
            "height": 61.0
        }
    },
    "m_Slots": [],
    "synonyms": [
        "code",
        "HLSL"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SourceType": 0,
    "m_FunctionName": "Enter function name here...",
    "m_FunctionSource": "",
    "m_FunctionBody": "Enter function body here..."
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "65dc9fa7434a451ca3ac9f192b7a812d",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "690973f342b94b32b515f19a5bd46494",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "6e05de95d94a407c93f26f3b9aa82fb8",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 8.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "6f899aedf89546c3ab6ac16a18d06f5d",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 0.78125,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2Node",
    "m_ObjectId": "707fa6983c9e45ea8b38fa94881c78af",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "Vector 2",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -787.5001220703125,
            "y": 328.5000915527344,
            "width": 127.0,
            "height": 76.99996948242188
        }
    },
    "m_Slots": [
        {
            "m_Id": "6f899aedf89546c3ab6ac16a18d06f5d"
        },
        {
            "m_Id": "dae6c399628e4bf587dd7123f4f833cd"
        },
        {
            "m_Id": "34a03a31a8964f4881f5d002a63e651d"
        }
    ],
    "synonyms": [
        "2",
        "v2",
        "vec2",
        "float2"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7730fcc93a2a4aed8f2704507b6e2dae",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "7ba43c2903a746678bbdc909495591d0",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Alpha",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "a68d9f02edfc438db7e71f493761f469"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Alpha"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "7bf1166be7484e618d1655ab47cc168b",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "cabc1f6561334be2aba206fcc517a02c"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DivideNode",
    "m_ObjectId": "7de828f883394fd1924d4a69baaf354c",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "Divide",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -916.5001220703125,
            "y": 328.5000915527344,
            "width": 126.0,
            "height": 117.99996948242188
        }
    },
    "m_Slots": [
        {
            "m_Id": "65dc9fa7434a451ca3ac9f192b7a812d"
        },
        {
            "m_Id": "bef070d0783642f5b50a82b1818b600f"
        },
        {
            "m_Id": "c7547b415ebb469f883ca10fff01d859"
        }
    ],
    "synonyms": [
        "division",
        "divided by"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "857c6e998a61493685609ce67d82f7d0",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Emission",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "e99630c228a24b838160e4b9ca619f3f"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Emission"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "870429cceb5a43ccb27389ccc2ee7239",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1Node",
    "m_ObjectId": "8bf6c322212f4f9c9d64bab7ea920dd8",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "Float",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -760.0001220703125,
            "y": 649.0001220703125,
            "width": 125.49993896484375,
            "height": 77.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "33de0718550a4118bb7452045af53d87"
        },
        {
            "m_Id": "43b694412f3b4051ad76e240a8139d84"
        }
    ],
    "synonyms": [
        "Vector 1",
        "1",
        "v1",
        "vec1",
        "scalar"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": 0.0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "8f7ffd8c635b4de086a7fdd8a4bc2080"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "93af4a934db049ab8e841b66e97950a7",
    "m_Id": 4,
    "m_DisplayName": "Texel Height",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texel Height",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "948e574c775a468fbde36d6075ed8ae2",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget",
    "m_ObjectId": "95e6edf6bfa1454ca0673681bc8ac466"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MultiplyNode",
    "m_ObjectId": "9991247fe903492ca56e8450e1276421",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "Multiply",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -919.5001220703125,
            "y": 200.00003051757813,
            "width": 129.0,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "f42aea2c10094fd3b47f3250cc5f7896"
        },
        {
            "m_Id": "bd5b7295e2df4a6ca4696c882f626ac1"
        },
        {
            "m_Id": "d4a855280d4948578ed8fed1704e5fa2"
        }
    ],
    "synonyms": [
        "multiplication",
        "times",
        "x"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "9c73d99ccf184de2b607bb050d84912a",
    "m_Id": 8,
    "m_DisplayName": "RadialOffset",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "RadialOffset",
    "m_StageCapability": 3,
    "m_Value": 0.6179999709129334,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "a252687d5f5c49da955fc66968eebac8",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "a300931a25914bdfb9693626948d22ca",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ScreenPositionNode",
    "m_ObjectId": "a3eb0cbef0084d12a8727dfea2c7ca00",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "Screen Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -805.5001220703125,
            "y": 62.50001525878906,
            "width": 145.0,
            "height": 128.49996948242188
        }
    },
    "m_Slots": [
        {
            "m_Id": "27b79a72632746e48c7eefeeab428cb0"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_ScreenSpaceType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a68d9f02edfc438db7e71f493761f469",
    "m_Id": 0,
    "m_DisplayName": "Alpha",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Alpha",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1Node",
    "m_ObjectId": "a78408b27ee4459685acc8000346fd8a",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "Float",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -760.0001220703125,
            "y": 495.00006103515627,
            "width": 125.49993896484375,
            "height": 77.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "6e05de95d94a407c93f26f3b9aa82fb8"
        },
        {
            "m_Id": "020769ddd3ff4228bed6d489da7e0a68"
        }
    ],
    "synonyms": [
        "Vector 1",
        "1",
        "v1",
        "vec1",
        "scalar"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": 0.0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2Node",
    "m_ObjectId": "a9076e381e6c417480382d7594819a28",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "Vector 2",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1217.000244140625,
            "y": 471.00006103515627,
            "width": 127.0,
            "height": 101.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "e146762c73254222a24cf8a349562b3d"
        },
        {
            "m_Id": "f3b56f78759d4c5790f6820214753075"
        },
        {
            "m_Id": "690973f342b94b32b515f19a5bd46494"
        }
    ],
    "synonyms": [
        "2",
        "v2",
        "vec2",
        "float2"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "a969794fd7694e05b735f5d87a7c5368",
    "m_Title": "Custom Function Node",
    "m_Content": "The Custom Function Node allows you to create your own node with any custom functionality inside of Shader Graph.\n\nYou can use the Graph Inspector to add inputs and outputs to the node. And then you can either reference an external HLSL code file or write your own HLSL function for the node. Writing in HLSL allows you to go beyond the limitations of Shader Graph and get access to everything the shading language can do.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -867.0000610351563,
        "y": -211.50001525878907,
        "width": 247.5,
        "height": 200.00001525878907
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "aa352fa0cda049d1b3a4cee2e46af7b9",
    "m_Id": 0,
    "m_DisplayName": "Width",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Width",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ae47cf8fbc9a4c51b291b41df64136ff",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SceneColorNode",
    "m_ObjectId": "aecca882c05d4abba454f3cb5cf8d6c8",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "Scene Color",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -769.0001220703125,
            "y": 803.0000610351563,
            "width": 138.0,
            "height": 77.00006103515625
        }
    },
    "m_Slots": [
        {
            "m_Id": "443a4122451045ffb7cfb247d4142d6b"
        },
        {
            "m_Id": "1298be9edda14f1e9e213a0fe4f0b5f5"
        }
    ],
    "synonyms": [
        "screen buffer"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "b11109c991c94ab692e83cff8e329a66",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "b739075bac224e6798a90c1f4c2f4633",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "bb6ed326c2304c0ba53c7a347dcf85a0",
    "m_Id": 9,
    "m_DisplayName": "ScreenPosition",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "ScreenPosition",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "bcb302d88bdc40c4b35156dbd0f0de93",
    "m_Id": 1,
    "m_DisplayName": "Height",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Height",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "bd5b7295e2df4a6ca4696c882f626ac1",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 2.0,
        "e01": 2.0,
        "e02": 2.0,
        "e03": 2.0,
        "e10": 2.0,
        "e11": 2.0,
        "e12": 2.0,
        "e13": 2.0,
        "e20": 2.0,
        "e21": 2.0,
        "e22": 2.0,
        "e23": 2.0,
        "e30": 2.0,
        "e31": 2.0,
        "e32": 2.0,
        "e33": 2.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "bef070d0783642f5b50a82b1818b600f",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 2.0,
        "y": 2.0,
        "z": 2.0,
        "w": 2.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "c14afcd87ebe46a8a770ba66c3b40cde",
    "m_ActiveSubTarget": {
        "m_Id": "8f7ffd8c635b4de086a7fdd8a4bc2080"
    },
    "m_Datas": [
        {
            "m_Id": "a300931a25914bdfb9693626948d22ca"
        },
        {
            "m_Id": "a252687d5f5c49da955fc66968eebac8"
        },
        {
            "m_Id": "44f2536efb364e8db6fafa129c0747bc"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "c720c9e4ccfe46418c3bfbbb89da1c8a",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "c7547b415ebb469f883ca10fff01d859",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "c99bc7915a1743e8abfad84b05ee0777",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "cabc1f6561334be2aba206fcc517a02c",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2Node",
    "m_ObjectId": "cc1f56ccd944497e9e88ccd366b5b05d",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "Vector 2",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1201.000244140625,
            "y": 122.0000228881836,
            "width": 127.0001220703125,
            "height": 100.99999237060547
        }
    },
    "m_Slots": [
        {
            "m_Id": "870429cceb5a43ccb27389ccc2ee7239"
        },
        {
            "m_Id": "017e23d345954753966e9680942fdb60"
        },
        {
            "m_Id": "61b009fe74c0483c8c8745d46123da7f"
        }
    ],
    "synonyms": [
        "2",
        "v2",
        "vec2",
        "float2"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "cfbf59fafbd04045826a367f33c88b63",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "d35f4f6e26d044649d18c68ddca18ac5",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "d4a855280d4948578ed8fed1704e5fa2",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "d5d59a0516344bdab2db4ef77fc5d163",
    "m_Id": 2,
    "m_DisplayName": "Cycles",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cycles",
    "m_StageCapability": 3,
    "m_Value": 16.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "dae6c399628e4bf587dd7123f4f833cd",
    "m_Id": 2,
    "m_DisplayName": "Y",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Y",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "Y"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1Node",
    "m_ObjectId": "dd3bb329b97d47db90312c1bf429329e",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "Float",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -760.0001220703125,
            "y": 726.0001220703125,
            "width": 125.49993896484375,
            "height": 76.99993896484375
        }
    },
    "m_Slots": [
        {
            "m_Id": "f7e84ec466564248928f6243304c8517"
        },
        {
            "m_Id": "3aa0f891c0fa4f1691ca174e68d9274b"
        }
    ],
    "synonyms": [
        "Vector 1",
        "1",
        "v1",
        "vec1",
        "scalar"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": 0.0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e146762c73254222a24cf8a349562b3d",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "e99630c228a24b838160e4b9ca619f3f",
    "m_Id": 0,
    "m_DisplayName": "Emission",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Emission",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 1,
    "m_DefaultColor": {
        "r": 0.0,
        "g": 0.0,
        "b": 0.0,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "ed44229b3d044a2cae0f48839f3513eb",
    "m_Id": 10,
    "m_DisplayName": "ScenePixels",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "ScenePixels",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ScreenNode",
    "m_ObjectId": "efc5fbb2eedb4fe786d4eddf8b763cf4",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "Screen",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1378.000244140625,
            "y": 250.5000762939453,
            "width": 87.5,
            "height": 100.99998474121094
        }
    },
    "m_Slots": [
        {
            "m_Id": "587bb630f14943f39d7bb860b9b867f4"
        },
        {
            "m_Id": "bcb302d88bdc40c4b35156dbd0f0de93"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DPropertiesNode",
    "m_ObjectId": "f00171cda8e249de8c0bd5d7b818dc5f",
    "m_Group": {
        "m_Id": "590ee3e0b3644cb580928a2787074b7c"
    },
    "m_Name": "Texture Size",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1399.************,
            "y": 471.00006103515627,
            "width": 181.5,
            "height": 101.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "aa352fa0cda049d1b3a4cee2e46af7b9"
        },
        {
            "m_Id": "1af9c50d75b84673a25a893068c244ff"
        },
        {
            "m_Id": "fb45bef7f2274fe3bee14b3d8fccb741"
        },
        {
            "m_Id": "93af4a934db049ab8e841b66e97950a7"
        },
        {
            "m_Id": "3f1c4076faf043a5adc206eda4d8c2fa"
        }
    ],
    "synonyms": [
        "texel size"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "f3b56f78759d4c5790f6820214753075",
    "m_Id": 2,
    "m_DisplayName": "Y",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Y",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "Y"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "f42aea2c10094fd3b47f3250cc5f7896",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "f7e84ec466564248928f6243304c8517",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 0.6179999709129334,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "f917db46ff7d4a0e9d66302d30c57761",
    "m_Id": 12,
    "m_DisplayName": "WidthHeightRatio",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "WidthHeightRatio",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "fb45bef7f2274fe3bee14b3d8fccb741",
    "m_Id": 3,
    "m_DisplayName": "Texel Width",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texel Width",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "fbb76735c8f541b790e2bd2f33a9ff16",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "fc17ead17bb04dea9e7b9cbd6b99986e",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 0.009999999776482582,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "fd7e4413a23541c7a92e62dd9adf33aa",
    "m_Id": 3,
    "m_DisplayName": "SamplesPerCycle",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "SamplesPerCycle",
    "m_StageCapability": 3,
    "m_Value": 8.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

