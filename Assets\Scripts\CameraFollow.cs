using UnityEngine;

/// <summary>
/// 相机跟随脚本
/// 提供平滑的第三人称相机跟随效果
/// </summary>
public class CameraFollow : MonoBehaviour
{
    [Header("跟随目标")]
    public Transform target;

    [Header("相机设置")]
    public Vector3 offset = new Vector3(0, 5, -7);
    public float followSpeed = 2f;
    public float rotationSpeed = 2f;

    [Header("视角设置")]
    public bool lookAtTarget = true;
    public Vector3 lookAtOffset = Vector3.up;
    
    private Transform cameraTransform;
    
    void Start()
    {
        cameraTransform = transform;
        
        // 如果没有设置目标，尝试找到玩家
        if (target == null)
        {
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                target = player.transform;
            }
            else
            {
                Debug.LogWarning("CameraFollow: 没有找到跟随目标！请设置Target或给玩家添加'Player'标签。");
            }
        }
    }
    
    void LateUpdate()
    {
        if (target == null) return;
        
        FollowTarget();
        
        if (lookAtTarget)
        {
            LookAtTarget();
        }
    }
    
    /// <summary>
    /// 跟随目标位置
    /// </summary>
    private void FollowTarget()
    {
        // 计算目标位置
        Vector3 targetPosition = target.position + offset;
        
        // 平滑移动到目标位置
        cameraTransform.position = Vector3.Lerp(
            cameraTransform.position, 
            targetPosition, 
            followSpeed * Time.deltaTime
        );
    }
    
    /// <summary>
    /// 看向目标
    /// </summary>
    private void LookAtTarget()
    {
        // 计算看向的目标点
        Vector3 lookAtPoint = target.position + lookAtOffset;
        
        // 计算目标旋转
        Vector3 direction = lookAtPoint - cameraTransform.position;
        if (direction != Vector3.zero)
        {
            Quaternion targetRotation = Quaternion.LookRotation(direction);
            
            // 平滑旋转
            cameraTransform.rotation = Quaternion.Slerp(
                cameraTransform.rotation, 
                targetRotation, 
                rotationSpeed * Time.deltaTime
            );
        }
    }
    
    /// <summary>
    /// 设置跟随目标
    /// </summary>
    /// <param name="newTarget">新的跟随目标</param>
    public void SetTarget(Transform newTarget)
    {
        target = newTarget;
    }
    
    /// <summary>
    /// 设置相机偏移
    /// </summary>
    /// <param name="newOffset">新的偏移值</param>
    public void SetOffset(Vector3 newOffset)
    {
        offset = newOffset;
    }
    
    /// <summary>
    /// 在Scene视图中绘制调试信息
    /// </summary>
    void OnDrawGizmosSelected()
    {
        if (target != null)
        {
            // 绘制目标位置
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(target.position, 0.5f);
            
            // 绘制相机目标位置
            Gizmos.color = Color.blue;
            Vector3 cameraTargetPos = target.position + offset;
            Gizmos.DrawWireSphere(cameraTargetPos, 0.3f);
            
            // 绘制连接线
            Gizmos.color = Color.yellow;
            Gizmos.DrawLine(target.position, cameraTargetPos);
            
            // 绘制视线方向
            if (lookAtTarget)
            {
                Gizmos.color = Color.green;
                Vector3 lookAtPoint = target.position + lookAtOffset;
                Gizmos.DrawLine(transform.position, lookAtPoint);
            }
        }
    }
}
