# Unity 超平坦角色移动测试

这是一个简单的Unity 3D角色移动测试项目，包含WASD移动控制和相机跟随功能。

## 脚本说明

### 1. PlayerController.cs
- **功能**: 角色移动控制器
- **控制**: 使用WASD键或方向键移动角色
- **特性**: 
  - 平滑移动和旋转
  - 角色面向移动方向
  - 使用CharacterController组件
  - 包含调试可视化

### 2. CameraFollow.cs
- **功能**: 第三人称相机跟随
- **特性**:
  - 平滑跟随玩家
  - 可调节偏移距离
  - 自动寻找Player标签的对象
  - 包含调试可视化

### 3. GameSetup.cs
- **功能**: 快速设置测试环境
- **特性**:
  - 自动创建地面、玩家和相机
  - 可通过Inspector或右键菜单执行
  - 支持清理测试环境

### 4. InputDebugUI.cs
- **功能**: 输入调试界面
- **特性**:
  - 实时显示输入状态
  - 显示玩家位置和旋转
  - 显示控制说明

## 快速开始

### 方法1: 使用GameSetup脚本（推荐）
1. 在场景中创建一个空的GameObject
2. 添加`GameSetup`脚本
3. 运行游戏，脚本会自动设置测试环境

### 方法2: 手动设置
1. **创建地面**:
   - 创建一个Cube，命名为"Ground"
   - 缩放为(20, 0.1, 20)
   - 位置设为(0, 0, 0)

2. **创建玩家**:
   - 创建一个Capsule，命名为"Player"
   - 添加"Player"标签
   - 添加`PlayerController`脚本
   - 位置设为(0, 1, 0)

3. **设置相机**:
   - 在Main Camera上添加`CameraFollow`脚本
   - 将Player拖拽到Target字段

4. **添加调试UI**（可选）:
   - 在场景中创建空GameObject
   - 添加`InputDebugUI`脚本

## 控制说明

- **W**: 向前移动
- **S**: 向后移动  
- **A**: 向左移动
- **D**: 向右移动

角色会自动面向移动方向，相机会平滑跟随角色。

## 参数调节

### PlayerController参数
- `Move Speed`: 移动速度
- `Rotation Speed`: 旋转速度

### CameraFollow参数
- `Offset`: 相机相对玩家的偏移位置
- `Follow Speed`: 跟随速度
- `Rotation Speed`: 旋转速度
- `Look At Target`: 是否看向目标

### GameSetup参数
- `Ground Size`: 地面大小
- `Player Start Position`: 玩家起始位置
- `Ground Material`: 地面材质
- `Player Material`: 玩家材质

## 调试功能

- Scene视图中会显示移动方向、相机连接线等调试信息
- 运行时左上角显示输入状态和玩家信息
- 可通过Inspector切换调试信息显示

## 注意事项

1. 确保项目使用URP渲染管线
2. 玩家对象需要"Player"标签才能被相机自动找到
3. 脚本会自动添加必要的组件（如CharacterController）
4. 建议在空场景中测试以避免冲突

## 扩展建议

- 添加跳跃功能
- 添加动画系统
- 添加音效
- 添加更多相机模式
- 添加障碍物和碰撞检测
