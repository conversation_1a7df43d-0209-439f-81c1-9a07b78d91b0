using UnityEngine;

/// <summary>
/// 简化版角色控制器
/// 确保兼容性的基础移动控制
/// </summary>
public class SimplePlayerController : MonoBehaviour
{
    public float moveSpeed = 5f;
    public float rotationSpeed = 720f;
    
    private CharacterController controller;
    
    void Start()
    {
        controller = GetComponent<CharacterController>();
        if (controller == null)
        {
            controller = gameObject.AddComponent<CharacterController>();
        }
    }
    
    void Update()
    {
        float horizontal = Input.GetAxis("Horizontal");
        float vertical = Input.GetAxis("Vertical");
        
        Vector3 direction = new Vector3(horizontal, 0, vertical);
        direction = direction.normalized;
        
        if (direction.magnitude >= 0.1f)
        {
            // 移动
            Vector3 moveVector = direction * moveSpeed * Time.deltaTime;
            moveVector.y = -9.81f * Time.deltaTime; // 重力
            controller.Move(moveVector);
            
            // 旋转
            Quaternion targetRotation = Quaternion.LookRotation(direction);
            transform.rotation = Quaternion.RotateTowards(transform.rotation, targetRotation, rotationSpeed * Time.deltaTime);
        }
        else
        {
            // 只应用重力
            Vector3 moveVector = new Vector3(0, -9.81f * Time.deltaTime, 0);
            controller.Move(moveVector);
        }
    }
}
