using UnityEngine;

/// <summary>
/// 游戏设置脚本
/// 用于快速创建测试环境
/// </summary>
public class GameSetup : MonoBehaviour
{
    [Header("自动设置")]
    public bool autoSetupOnStart = true;

    [Header("地面设置")]
    public Vector3 groundSize = new Vector3(20, 0.1f, 20);
    public Material groundMaterial;

    [Header("玩家设置")]
    public Vector3 playerStartPosition = Vector3.up;
    public Material playerMaterial;
    
    void Start()
    {
        if (autoSetupOnStart)
        {
            SetupTestEnvironment();
        }
    }
    
    /// <summary>
    /// 设置测试环境
    /// </summary>
    [ContextMenu("设置测试环境")]
    public void SetupTestEnvironment()
    {
        CreateGround();
        CreatePlayer();
        SetupCamera();
        
        Debug.Log("测试环境设置完成！使用WASD键移动角色。");
    }
    
    /// <summary>
    /// 创建地面
    /// </summary>
    private void CreateGround()
    {
        GameObject ground = GameObject.Find("Ground");
        if (ground == null)
        {
            ground = GameObject.CreatePrimitive(PrimitiveType.Cube);
            ground.name = "Ground";
        }
        
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = groundSize;
        
        // 设置材质
        Renderer groundRenderer = ground.GetComponent<Renderer>();
        if (groundMaterial != null)
        {
            groundRenderer.material = groundMaterial;
        }
        else
        {
            // 创建简单的灰色材质
            Material defaultMaterial = new Material(Shader.Find("Standard"));
            defaultMaterial.color = Color.gray;
            groundRenderer.material = defaultMaterial;
        }
        
        // 添加标签
        ground.tag = "Ground";
    }
    
    /// <summary>
    /// 创建玩家
    /// </summary>
    private void CreatePlayer()
    {
        GameObject player = GameObject.Find("Player");
        if (player == null)
        {
            player = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            player.name = "Player";
        }
        
        player.transform.position = playerStartPosition;
        player.tag = "Player";
        
        // 移除默认的碰撞器（因为我们使用CharacterController）
        Collider defaultCollider = player.GetComponent<Collider>();
        if (defaultCollider != null)
        {
            DestroyImmediate(defaultCollider);
        }
        
        // 添加PlayerController组件
        if (player.GetComponent<PlayerController>() == null)
        {
            player.AddComponent<PlayerController>();
        }
        
        // 设置材质
        Renderer playerRenderer = player.GetComponent<Renderer>();
        if (playerMaterial != null)
        {
            playerRenderer.material = playerMaterial;
        }
        else
        {
            // 创建简单的蓝色材质
            Material defaultMaterial = new Material(Shader.Find("Standard"));
            defaultMaterial.color = Color.blue;
            playerRenderer.material = defaultMaterial;
        }
    }
    
    /// <summary>
    /// 设置相机
    /// </summary>
    private void SetupCamera()
    {
        Camera mainCamera = Camera.main;
        if (mainCamera == null)
        {
            GameObject cameraObj = new GameObject("Main Camera");
            mainCamera = cameraObj.AddComponent<Camera>();
            cameraObj.tag = "MainCamera";
        }
        
        // 添加相机跟随脚本
        CameraFollow cameraFollow = mainCamera.GetComponent<CameraFollow>();
        if (cameraFollow == null)
        {
            cameraFollow = mainCamera.gameObject.AddComponent<CameraFollow>();
        }
        
        // 设置相机跟随目标
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            cameraFollow.SetTarget(player.transform);
        }
    }
    
    /// <summary>
    /// 清理测试环境
    /// </summary>
    [ContextMenu("清理测试环境")]
    public void CleanupTestEnvironment()
    {
        GameObject ground = GameObject.Find("Ground");
        GameObject player = GameObject.Find("Player");
        
        if (ground != null) DestroyImmediate(ground);
        if (player != null) DestroyImmediate(player);
        
        Debug.Log("测试环境已清理。");
    }
}
