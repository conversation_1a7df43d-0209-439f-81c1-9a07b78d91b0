using UnityEngine;

/// <summary>
/// 最简单的设置脚本 - 确保无编译错误
/// </summary>
public class EasySetup : MonoBehaviour
{
    void Start()
    {
        Debug.Log("=== EasySetup 开始 ===");
        
        // 创建第一人称玩家
        CreatePlayer();
        
        Debug.Log("=== 设置完成！===");
        Debug.Log("使用WASD移动，鼠标控制视角");
    }
    
    void CreatePlayer()
    {
        // 检查是否已有玩家
        if (GameObject.Find("FPSPlayer") != null)
        {
            Debug.Log("玩家已存在，跳过创建");
            return;
        }
        
        // 创建玩家对象
        GameObject player = new GameObject("FPSPlayer");
        player.transform.position = new Vector3(0, 1, 0);
        
        // 添加SimpleFirstPerson脚本
        player.AddComponent<SimpleFirstPerson>();
        
        Debug.Log("第一人称玩家创建完成");
    }
}
