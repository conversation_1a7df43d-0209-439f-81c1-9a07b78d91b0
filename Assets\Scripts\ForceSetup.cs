using UnityEngine;

public class ForceSetup : MonoBehaviour
{
    void Awake()
    {
        Debug.Log("=== ForceSetup Awake 开始 ===");
        CreateObjects();
    }
    
    void Start()
    {
        Debug.Log("=== ForceSetup Start 开始 ===");
        CreateObjects();
    }
    
    void CreateObjects()
    {
        Debug.Log("开始强制创建对象...");
        
        // 创建地面
        if (GameObject.Find("TestGround") == null)
        {
            GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Cube);
            ground.name = "TestGround";
            ground.transform.position = Vector3.zero;
            ground.transform.localScale = new Vector3(10, 0.1f, 10);
            Debug.Log("地面已创建: " + ground.name);
        }
        
        // 创建玩家
        if (GameObject.Find("TestPlayer") == null)
        {
            GameObject player = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            player.name = "TestPlayer";
            player.transform.position = new Vector3(0, 1, 0);
            player.AddComponent<TestMovement>();
            
            // 设置颜色
            Renderer r = player.GetComponent<Renderer>();
            Material m = new Material(Shader.Find("Standard"));
            m.color = Color.red;
            r.material = m;
            
            Debug.Log("玩家已创建: " + player.name);
        }
        
        Debug.Log("对象创建完成！");
    }
    
    void Update()
    {
        if (Input.GetKeyDown(KeyCode.T))
        {
            Debug.Log("按下T键，重新创建对象");
            CreateObjects();
        }
    }
}
