{"name": "Unity.VisualScripting.Flow.Editor", "references": ["Unity.VisualScripting.Core", "Unity.VisualScripting.Core.Editor", "Unity.VisualScripting.Flow", "Unity.InputSystem"], "optionalUnityReferences": [], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "versionDefines": [{"name": "com.unity.inputsystem", "expression": "1.0.1", "define": "PACKAGE_INPUT_SYSTEM_EXISTS"}, {"name": "com.unity.inputsystem", "expression": "1.2.0", "define": "PACKAGE_INPUT_SYSTEM_1_2_0_OR_NEWER_EXISTS"}]}