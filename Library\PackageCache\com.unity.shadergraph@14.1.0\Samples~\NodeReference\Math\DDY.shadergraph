{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "a6a2c084b83c48dd8da3ccd3c87a8680",
    "m_Properties": [],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "fe4c3cc527364c06b551d113c4e0fb2f"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "60b69faeaa0b4a2bb8158bfebe048214"
        },
        {
            "m_Id": "45f91cf7fd434f8a91c5ef6ff7fd6043"
        },
        {
            "m_Id": "e05ec86b78a747e982591b586d73fa7f"
        },
        {
            "m_Id": "d1e9246b2c0c441786c33e492c72e7d8"
        },
        {
            "m_Id": "502f9eb8b4d54c549f374a170bf3c6c5"
        },
        {
            "m_Id": "d5750300ed654bab86d8bc4c2a237d5a"
        },
        {
            "m_Id": "7d60ed2d45894499bfd63a62a3480828"
        },
        {
            "m_Id": "1d29654110be4cb0bb650a6622f5d732"
        },
        {
            "m_Id": "657f1cf219a94cd7a2310cb268b68f65"
        },
        {
            "m_Id": "c8f8e7f6846d46b196fb24d3efa0cf0c"
        },
        {
            "m_Id": "6af27eea542940ca93202993596f97cc"
        },
        {
            "m_Id": "5709936673d54dc087a5dc83b02f67f7"
        },
        {
            "m_Id": "ddc7448660f345db93a17963f1c89d4b"
        },
        {
            "m_Id": "4b738cc89e5545ec9176eb648c74cdaf"
        },
        {
            "m_Id": "7ec8fc34c10b45769dfba44fa288e347"
        },
        {
            "m_Id": "760c77d738124ec0b1ab818ce52f20af"
        },
        {
            "m_Id": "0727670469e04b478aca47089e0f5fc1"
        },
        {
            "m_Id": "d22dcef498794ebcb87bf13ecbc76adf"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "6251f5292d5b4c228867ceded03d9afe"
        },
        {
            "m_Id": "8a8e4fdd30af4113858058452dd4b863"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "aa110b4d7c0b482eaed378ff91dbd031"
        },
        {
            "m_Id": "f6e28907f1984a54bb766b660adddbd6"
        },
        {
            "m_Id": "3792d264843e49b2a45b266a0c9f30e5"
        },
        {
            "m_Id": "858faf9f38fb4cbbb5b37f9bd006bc9b"
        },
        {
            "m_Id": "14a4a7ad6ec74a649a3185d23e7ee435"
        },
        {
            "m_Id": "c3880d0b64a44c279e66d77307533054"
        },
        {
            "m_Id": "a5281e60769f4e56b27bed6767caff1b"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "1d29654110be4cb0bb650a6622f5d732"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "657f1cf219a94cd7a2310cb268b68f65"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "4b738cc89e5545ec9176eb648c74cdaf"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5709936673d54dc087a5dc83b02f67f7"
                },
                "m_SlotId": 10
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "502f9eb8b4d54c549f374a170bf3c6c5"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "7d60ed2d45894499bfd63a62a3480828"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "502f9eb8b4d54c549f374a170bf3c6c5"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "d5750300ed654bab86d8bc4c2a237d5a"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "657f1cf219a94cd7a2310cb268b68f65"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "c8f8e7f6846d46b196fb24d3efa0cf0c"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "760c77d738124ec0b1ab818ce52f20af"
                },
                "m_SlotId": 4
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5709936673d54dc087a5dc83b02f67f7"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "7d60ed2d45894499bfd63a62a3480828"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1d29654110be4cb0bb650a6622f5d732"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "7ec8fc34c10b45769dfba44fa288e347"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5709936673d54dc087a5dc83b02f67f7"
                },
                "m_SlotId": 11
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "d5750300ed654bab86d8bc4c2a237d5a"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "1d29654110be4cb0bb650a6622f5d732"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "ddc7448660f345db93a17963f1c89d4b"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "4b738cc89e5545ec9176eb648c74cdaf"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "ddc7448660f345db93a17963f1c89d4b"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "7ec8fc34c10b45769dfba44fa288e347"
                },
                "m_SlotId": 0
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "60b69faeaa0b4a2bb8158bfebe048214"
            },
            {
                "m_Id": "45f91cf7fd434f8a91c5ef6ff7fd6043"
            },
            {
                "m_Id": "e05ec86b78a747e982591b586d73fa7f"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "d1e9246b2c0c441786c33e492c72e7d8"
            },
            {
                "m_Id": "0727670469e04b478aca47089e0f5fc1"
            },
            {
                "m_Id": "d22dcef498794ebcb87bf13ecbc76adf"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_ActiveTargets": [
        {
            "m_Id": "2bc7a627f0e348898dee024ea701938b"
        },
        {
            "m_Id": "06c4c5168e9c47258ce594da7643fd3a"
        },
        {
            "m_Id": "ee1e4c88cb064cf88766bfda4fb0106b"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "03e21359f2304ae892036fa830a2e2be",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 1.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "04f5cfd7130c4e0ea45ccbf12e362151",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "05746b51af734863b728d28236d8e2c7",
    "m_Id": 4,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "06c4c5168e9c47258ce594da7643fd3a",
    "m_ActiveSubTarget": {
        "m_Id": "eca9a1892f5f44619758a8a8f8688630"
    },
    "m_Datas": [
        {
            "m_Id": "54f18cfc71e14a4984aa0f2a344d84eb"
        },
        {
            "m_Id": "8baa8a4e83434c26836a078408b015b9"
        },
        {
            "m_Id": "8d23bdc81d6841b49626e72f5bc59ab1"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "0727670469e04b478aca47089e0f5fc1",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Emission",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "842fb949ee2c46ee971e3165d52e7320"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Emission"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0c3364d3ed664b8ab749033b6f3cebfb",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "0c36a0cb6f0540d19e52e321f3ce5947"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "14a4a7ad6ec74a649a3185d23e7ee435",
    "m_Title": "",
    "m_Content": "This illustrates how to create Face Normals using DDX and DDY.  This is useful for meshes where you've altered the vertex normals - like you might for foliage - but you still need to use the real normal for some things.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1946.5001220703125,
        "y": 407.0000305175781,
        "width": 200.0,
        "height": 118.50003051757813
    },
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "162162b5d3fa403393ac21e67c0d6529",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CrossProductNode",
    "m_ObjectId": "1d29654110be4cb0bb650a6622f5d732",
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    },
    "m_Name": "Cross Product",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1529.000244140625,
            "y": 81.99996948242188,
            "width": 129.5,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "93355243d2cb4a958865044033e8ebd7"
        },
        {
            "m_Id": "03e21359f2304ae892036fa830a2e2be"
        },
        {
            "m_Id": "a915288d689b4ea1ae429469c9a34c38"
        }
    ],
    "synonyms": [
        "perpendicular"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "1e6fe2c5e7834a709519e5d31d54cbe1",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "2505d721f0514a0e9afa9633771a0692",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "27b530bdc2de483886c08ab551dff173",
    "m_Id": 11,
    "m_DisplayName": "DDY",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "DDY",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "2bc7a627f0e348898dee024ea701938b",
    "m_ActiveSubTarget": {
        "m_Id": "d0335300a26b40da9d292c9dee62ba76"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget",
    "m_ObjectId": "316c1535eded4ac9bf05c74ad5c2fb3d"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "31b7e95a3bb94710a322c7c1a4481b4e",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "3792d264843e49b2a45b266a0c9f30e5",
    "m_Title": "",
    "m_Content": "Doing a cross product between those two slope vectors creates a surface normal.  Notice that it's faceted.  This is because we're not using the smoothed vertex normals, but instead, we're generating the normals purely from the shape of the geometry.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1488.0001220703125,
        "y": 367.5000305175781,
        "width": 200.0,
        "height": 125.00003051757813
    },
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "45f91cf7fd434f8a91c5ef6ff7fd6043",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "8187c346069145e3bfbf5e8d27025cf9"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "465b07be7f3f421e80358e1a381b3cb8",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DDXNode",
    "m_ObjectId": "4b738cc89e5545ec9176eb648c74cdaf",
    "m_Group": {
        "m_Id": "8a8e4fdd30af4113858058452dd4b863"
    },
    "m_Name": "DDX",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -706.5000610351563,
            "y": 146.50001525878907,
            "width": 131.5,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "04f5cfd7130c4e0ea45ccbf12e362151"
        },
        {
            "m_Id": "b21df695904d4f96a28ddfdf30e3d391"
        }
    ],
    "synonyms": [
        "derivative"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.PositionNode",
    "m_ObjectId": "502f9eb8b4d54c549f374a170bf3c6c5",
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    },
    "m_Name": "Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1944.000244140625,
            "y": 91.0000228881836,
            "width": 206.0,
            "height": 130.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "65ee3446f8ca4b74a7e93f35ccc30c0e"
        }
    ],
    "synonyms": [
        "location"
    ],
    "m_Precision": 1,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 2,
    "m_PositionSource": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "54f18cfc71e14a4984aa0f2a344d84eb",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "5709936673d54dc087a5dc83b02f67f7",
    "m_Group": {
        "m_Id": "8a8e4fdd30af4113858058452dd4b863"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -486.5,
            "y": 47.5,
            "width": 208.0,
            "height": 386.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "ffcdf4d37c2846b69b87060346f00d86"
        },
        {
            "m_Id": "adcb591370fd4b33ae74a78c7ec87d25"
        },
        {
            "m_Id": "b1624783e7c84effadebc8802e9697f6"
        },
        {
            "m_Id": "6f9b0b7077b34668afa8aecf2252f5d6"
        },
        {
            "m_Id": "0c3364d3ed664b8ab749033b6f3cebfb"
        },
        {
            "m_Id": "a4fdbaeded03451f9d884639b74eea7f"
        },
        {
            "m_Id": "8e14d3b6cd0a407f8dbf54eedea2b0bd"
        },
        {
            "m_Id": "9745b326cd5442da8183b9f334737ae6"
        },
        {
            "m_Id": "b527a15f0cf14d1ab6851ae3bfa69050"
        },
        {
            "m_Id": "27b530bdc2de483886c08ab551dff173"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 0,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 2
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "60b69faeaa0b4a2bb8158bfebe048214",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "efdf4183f23b467ca4bd049122142568"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "6251f5292d5b4c228867ceded03d9afe",
    "m_Title": "Generating Face Normals",
    "m_Position": {
        "x": -1971.500244140625,
        "y": -7.499958038330078
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "63fb1cfc046546db992e7a697ed1b372",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalizeNode",
    "m_ObjectId": "657f1cf219a94cd7a2310cb268b68f65",
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    },
    "m_Name": "Normalize",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1399.500244140625,
            "y": 81.99996948242188,
            "width": 208.0,
            "height": 278.0000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "8f8236f0e3eb4ed7866febf3dbe4ecbf"
        },
        {
            "m_Id": "1e6fe2c5e7834a709519e5d31d54cbe1"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "65ee3446f8ca4b74a7e93f35ccc30c0e",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "6a5f47ae933c41e99f226b6e60ba57ce",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DDYNode",
    "m_ObjectId": "6af27eea542940ca93202993596f97cc",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "DDY",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1150.5001220703125,
            "y": -189.0000457763672,
            "width": 127.50006103515625,
            "height": 94.00006103515625
        }
    },
    "m_Slots": [
        {
            "m_Id": "faab6d0194c84d7692871ae36f87ac54"
        },
        {
            "m_Id": "7bb41db0de014ee28a01845e2c5dfd92"
        }
    ],
    "synonyms": [
        "derivative"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "6c33847e82a64297b2453d99c5fedaba",
    "m_Id": 1,
    "m_DisplayName": "Center",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Center",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.5,
        "y": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "6f9b0b7077b34668afa8aecf2252f5d6",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PolarCoordinatesNode",
    "m_ObjectId": "760c77d738124ec0b1ab818ce52f20af",
    "m_Group": {
        "m_Id": "8a8e4fdd30af4113858058452dd4b863"
    },
    "m_Name": "Polar Coordinates",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -870.0000610351563,
            "y": 47.5,
            "width": 147.5,
            "height": 94.00001525878906
        }
    },
    "m_Slots": [
        {
            "m_Id": "465b07be7f3f421e80358e1a381b3cb8"
        },
        {
            "m_Id": "6c33847e82a64297b2453d99c5fedaba"
        },
        {
            "m_Id": "f17273a8d5214daa8e66c5077e6273ec"
        },
        {
            "m_Id": "8763468e99e540529cb3463f45eb861a"
        },
        {
            "m_Id": "05746b51af734863b728d28236d8e2c7"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "7bb41db0de014ee28a01845e2c5dfd92",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DDYNode",
    "m_ObjectId": "7d60ed2d45894499bfd63a62a3480828",
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    },
    "m_Name": "DDY",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1689.000244140625,
            "y": 47.5,
            "width": 131.5,
            "height": 94.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "85cc5228dd8546338a8aefc6c0946192"
        },
        {
            "m_Id": "162162b5d3fa403393ac21e67c0d6529"
        }
    ],
    "synonyms": [
        "derivative"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DDYNode",
    "m_ObjectId": "7ec8fc34c10b45769dfba44fa288e347",
    "m_Group": {
        "m_Id": "8a8e4fdd30af4113858058452dd4b863"
    },
    "m_Name": "DDY",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -706.5000610351563,
            "y": 240.50001525878907,
            "width": 131.5,
            "height": 93.99998474121094
        }
    },
    "m_Slots": [
        {
            "m_Id": "cc1f9b13adf348b2b5b67b57f47e9142"
        },
        {
            "m_Id": "e5a759d7b4b84b268097d8e1058a7efe"
        }
    ],
    "synonyms": [
        "derivative"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "8187c346069145e3bfbf5e8d27025cf9",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "842fb949ee2c46ee971e3165d52e7320",
    "m_Id": 0,
    "m_DisplayName": "Emission",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Emission",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 1,
    "m_DefaultColor": {
        "r": 0.0,
        "g": 0.0,
        "b": 0.0,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "843e28aca1ea4059a9038add136eaa51",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "858faf9f38fb4cbbb5b37f9bd006bc9b",
    "m_Title": "",
    "m_Content": "The results are in World space, so we have to transform to Tangent space if we want to plug the result into the Master Stack.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1166.0001220703125,
        "y": 425.0000305175781,
        "width": 200.00006103515626,
        "height": 100.00003051757813
    },
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "85cc5228dd8546338a8aefc6c0946192",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "8763468e99e540529cb3463f45eb861a",
    "m_Id": 3,
    "m_DisplayName": "Length Scale",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "LengthScale",
    "m_StageCapability": 3,
    "m_Value": 4.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "8a8e4fdd30af4113858058452dd4b863",
    "m_Title": "Manually Create Texture UV Derivatives",
    "m_Position": {
        "x": -915.5000610351563,
        "y": -11.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "8baa8a4e83434c26836a078408b015b9",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "8d23bdc81d6841b49626e72f5bc59ab1",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "8e14d3b6cd0a407f8dbf54eedea2b0bd",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "8f8236f0e3eb4ed7866febf3dbe4ecbf",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "93355243d2cb4a958865044033e8ebd7",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "9745b326cd5442da8183b9f334737ae6",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "9ea975d2954b4edc86fb5d0d133d5965",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "a1b8b00a8937468789ac1216bf31e6e3",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "a4fdbaeded03451f9d884639b74eea7f",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"583384ba064432b41891bec94e35c8af\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "a5281e60769f4e56b27bed6767caff1b",
    "m_Title": "",
    "m_Content": "By default, a texture sampler uses DDX and DDY internally to compute the proper mip level.  Here we're doing it manually to fix a discontinuity seam.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -798.0000610351563,
        "y": 383.0000305175781,
        "width": 200.0,
        "height": 100.00003051757813
    },
    "m_Group": {
        "m_Id": "8a8e4fdd30af4113858058452dd4b863"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "a915288d689b4ea1ae429469c9a34c38",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "a9617e2b910c43c5933b5416d6bdae9a",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "aa110b4d7c0b482eaed378ff91dbd031",
    "m_Title": "DDY Node",
    "m_Content": "The DDY Node allows you to find the difference (the partial derivative) between data in the current pixel and data in the next pixel above in screen space.\n\nThis node only works in the fragment stage.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1013.0000610351563,
        "y": -199.50001525878907,
        "width": 200.0,
        "height": 143.89132690429688
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "adcb591370fd4b33ae74a78c7ec87d25",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b1624783e7c84effadebc8802e9697f6",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "b21df695904d4f96a28ddfdf30e3d391",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "b527a15f0cf14d1ab6851ae3bfa69050",
    "m_Id": 10,
    "m_DisplayName": "DDX",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "DDX",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "c3880d0b64a44c279e66d77307533054",
    "m_Title": "",
    "m_Content": "When using polar coordinates, you get an ugly seam where the UVs wrap around and meet up. This can be fixed by manually computing the texture mip map derivatives using DDX and DDY.  You need to set the Sample Texture 2D node to \"Gradient\" mode in the Graph Inspector first.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -482.0000305175781,
        "y": 440.5000305175781,
        "width": 200.0,
        "height": 148.00003051757813
    },
    "m_Group": {
        "m_Id": "8a8e4fdd30af4113858058452dd4b863"
    }
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.ShaderGraph.TransformNode",
    "m_ObjectId": "c8f8e7f6846d46b196fb24d3efa0cf0c",
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    },
    "m_Name": "Transform",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1170.000244140625,
            "y": 81.99996948242188,
            "width": 212.50006103515626,
            "height": 340.00006103515627
        }
    },
    "m_Slots": [
        {
            "m_Id": "63fb1cfc046546db992e7a697ed1b372"
        },
        {
            "m_Id": "2505d721f0514a0e9afa9633771a0692"
        }
    ],
    "synonyms": [
        "world",
        "tangent",
        "object",
        "view",
        "screen",
        "convert"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Conversion": {
        "from": 2,
        "to": 3
    },
    "m_ConversionType": 2,
    "m_Normalize": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "cc1f9b13adf348b2b5b67b57f47e9142",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInUnlitSubTarget",
    "m_ObjectId": "d0335300a26b40da9d292c9dee62ba76"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "d1198a697b654d34bd8d50bf2fb53343",
    "m_Id": 0,
    "m_DisplayName": "Alpha",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Alpha",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "d1e9246b2c0c441786c33e492c72e7d8",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "a9617e2b910c43c5933b5416d6bdae9a"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "d22dcef498794ebcb87bf13ecbc76adf",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Alpha",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "d1198a697b654d34bd8d50bf2fb53343"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Alpha"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DDXNode",
    "m_ObjectId": "d5750300ed654bab86d8bc4c2a237d5a",
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    },
    "m_Name": "DDX",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1689.000244140625,
            "y": 141.50003051757813,
            "width": 131.5,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "843e28aca1ea4059a9038add136eaa51"
        },
        {
            "m_Id": "9ea975d2954b4edc86fb5d0d133d5965"
        }
    ],
    "synonyms": [
        "derivative"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "dd748b3793824a8fb308932007f01b4c",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVNode",
    "m_ObjectId": "ddc7448660f345db93a17963f1c89d4b",
    "m_Group": {
        "m_Id": "8a8e4fdd30af4113858058452dd4b863"
    },
    "m_Name": "UV",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -890.5000610351563,
            "y": 180.50001525878907,
            "width": 145.0,
            "height": 128.49998474121095
        }
    },
    "m_Slots": [
        {
            "m_Id": "31b7e95a3bb94710a322c7c1a4481b4e"
        }
    ],
    "synonyms": [
        "texcoords",
        "coords",
        "coordinates"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_OutputChannel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "e05ec86b78a747e982591b586d73fa7f",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "a1b8b00a8937468789ac1216bf31e6e3"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "e5a759d7b4b84b268097d8e1058a7efe",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "eca9a1892f5f44619758a8a8f8688630"
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "ee1e4c88cb064cf88766bfda4fb0106b",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "316c1535eded4ac9bf05c74ad5c2fb3d"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "efdf4183f23b467ca4bd049122142568",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "f17273a8d5214daa8e66c5077e6273ec",
    "m_Id": 2,
    "m_DisplayName": "Radial Scale",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "RadialScale",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "f3526e0cf4ce4d72bcd7f5d8c68fe538",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "f6e28907f1984a54bb766b660adddbd6",
    "m_Title": "",
    "m_Content": "DDX and DDY find the difference between the position of the current pixel and the one next to it - horizontally (for DDX), and vertically (for DDY). ",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1774.0001220703125,
        "y": 244.50003051757813,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "6251f5292d5b4c228867ceded03d9afe"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "faab6d0194c84d7692871ae36f87ac54",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "fe4c3cc527364c06b551d113c4e0fb2f",
    "m_Name": "",
    "m_ChildObjectList": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "ffcdf4d37c2846b69b87060346f00d86",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

